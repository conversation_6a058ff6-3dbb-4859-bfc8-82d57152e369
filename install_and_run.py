#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تثبيت المكتبات وتشغيل محرر الفيديوهات
Install Libraries and Run Video Editor
"""

import subprocess
import sys
import os
import importlib.util

def check_library(library_name, import_name=None):
    """فحص وجود مكتبة معينة"""
    if import_name is None:
        import_name = library_name
    
    spec = importlib.util.find_spec(import_name)
    return spec is not None

def install_library(library_name):
    """تثبيت مكتبة معينة"""
    try:
        print(f"جاري تثبيت {library_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", library_name])
        print(f"✅ تم تثبيت {library_name} بنجاح")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ فشل في تثبيت {library_name}")
        return False

def install_required_libraries():
    """تثبيت جميع المكتبات المطلوبة"""
    required_libraries = [
        ("moviepy", "moviepy"),
        ("opencv-python", "cv2"),
        ("pillow", "PIL"),
        ("numpy", "numpy"),
        ("pygame", "pygame"),
        ("imageio", "imageio"),
        ("imageio-ffmpeg", "imageio_ffmpeg"),
    ]
    
    optional_libraries = [
        ("pyaudio", "pyaudio"),  # للتسجيل الصوتي
    ]
    
    print("🔍 فحص المكتبات المطلوبة...")
    
    missing_libraries = []
    for lib_name, import_name in required_libraries:
        if not check_library(lib_name, import_name):
            missing_libraries.append(lib_name)
    
    if missing_libraries:
        print(f"📦 المكتبات المفقودة: {', '.join(missing_libraries)}")
        
        for lib_name in missing_libraries:
            install_library(lib_name)
    else:
        print("✅ جميع المكتبات المطلوبة مثبتة")
    
    # تثبيت المكتبات الاختيارية
    print("\n🔍 فحص المكتبات الاختيارية...")
    for lib_name, import_name in optional_libraries:
        if not check_library(lib_name, import_name):
            print(f"⚠️ المكتبة الاختيارية {lib_name} غير مثبتة")
            choice = input(f"هل تريد تثبيت {lib_name}؟ (y/n): ").lower()
            if choice in ['y', 'yes', 'نعم']:
                install_library(lib_name)

def check_ffmpeg():
    """فحص وجود FFmpeg"""
    try:
        subprocess.run(["ffmpeg", "-version"], capture_output=True, check=True)
        print("✅ FFmpeg مثبت ومتاح")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️ FFmpeg غير مثبت أو غير متاح في PATH")
        print("💡 نصيحة: قم بتنزيل FFmpeg من https://ffmpeg.org/download.html")
        return False

def run_video_editor():
    """تشغيل محرر الفيديوهات"""
    try:
        print("\n🚀 تشغيل محرر الفيديوهات...")
        
        # التأكد من وجود الملف
        if not os.path.exists("video_editor_with_comments.py"):
            print("❌ ملف محرر الفيديوهات غير موجود!")
            return False
        
        # تشغيل البرنامج
        import video_editor_with_comments
        video_editor_with_comments.main()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل محرر الفيديوهات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🎬 محرر الفيديوهات مع التعليقات والعلامة المائية")
    print("Video Editor with Comments and Watermark")
    print("=" * 60)
    
    # فحص وتثبيت المكتبات
    install_required_libraries()
    
    # فحص FFmpeg
    print("\n" + "=" * 40)
    check_ffmpeg()
    
    # تشغيل البرنامج
    print("\n" + "=" * 40)
    success = run_video_editor()
    
    if not success:
        print("\n❌ فشل في تشغيل البرنامج")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
