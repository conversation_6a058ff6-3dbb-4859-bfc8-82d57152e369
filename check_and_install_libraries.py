#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import subprocess
import importlib.util
import os

def check_library(library_name, import_name=None):
    """
    فحص وجود مكتبة معينة
    """
    if import_name is None:
        import_name = library_name
    
    try:
        spec = importlib.util.find_spec(import_name)
        if spec is not None:
            print(f"✅ {library_name} مثبتة")
            return True
        else:
            print(f"❌ {library_name} غير مثبتة")
            return False
    except ImportError:
        print(f"❌ {library_name} غير مثبتة")
        return False

def check_ffmpeg():
    """
    فحص وجود ffmpeg
    """
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ ffmpeg مثبت")
            version_line = result.stdout.split('\n')[0]
            print(f"   الإصدار: {version_line}")
            return True
        else:
            print("❌ ffmpeg غير مثبت")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ ffmpeg غير مثبت")
        return False

def install_library(library_name):
    """
    تثبيت مكتبة باستخدام pip
    """
    try:
        print(f"🔄 جاري تثبيت {library_name}...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', library_name], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ تم تثبيت {library_name} بنجاح")
            return True
        else:
            print(f"❌ فشل في تثبيت {library_name}")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ خطأ في تثبيت {library_name}: {e}")
        return False

def install_ffmpeg_windows():
    """
    محاولة تثبيت ffmpeg على Windows
    """
    print("🔄 محاولة تثبيت ffmpeg...")
    
    # محاولة استخدام winget
    try:
        result = subprocess.run(['winget', 'install', 'ffmpeg'], 
                              capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print("✅ تم تثبيت ffmpeg باستخدام winget")
            return True
    except:
        pass
    
    # محاولة استخدام chocolatey
    try:
        result = subprocess.run(['choco', 'install', 'ffmpeg', '-y'], 
                              capture_output=True, text=True, timeout=120)
        if result.returncode == 0:
            print("✅ تم تثبيت ffmpeg باستخدام chocolatey")
            return True
    except:
        pass
    
    print("❌ فشل في التثبيت التلقائي لـ ffmpeg")
    return False

def main():
    print("🔍 فحص المكتبات المطلوبة...")
    print("=" * 60)
    
    # معلومات النظام
    print(f"🐍 Python: {sys.version}")
    print(f"💻 النظام: {os.name}")
    print("=" * 60)
    
    # قائمة المكتبات المطلوبة
    required_libraries = [
        ('Pillow', 'PIL'),
        ('moviepy', 'moviepy'),
        ('numpy', 'numpy'),
        ('python-bidi', 'bidi'),
        ('arabic-reshaper', 'arabic_reshaper'),
        ('trimesh', 'trimesh'),
        ('matplotlib', 'matplotlib'),
        ('imageio', 'imageio'),
        ('imageio-ffmpeg', 'imageio_ffmpeg'),
        ('requests', 'requests'),
        ('opencv-python', 'cv2'),
        ('pandas', 'pandas'),
        ('openpyxl', 'openpyxl'),
        ('python-docx', 'docx'),
        ('python-pptx', 'pptx'),
        ('gTTS', 'gtts'),
        ('pygame', 'pygame'),
        ('customtkinter', 'customtkinter'),
        ('fpdf2', 'fpdf'),
        ('XlsxWriter', 'xlsxwriter'),
        ('Flask', 'flask'),
        ('beautifulsoup4', 'bs4'),
        ('lxml', 'lxml'),
        ('tqdm', 'tqdm')
    ]
    
    missing_libraries = []
    
    # فحص كل مكتبة
    print("📦 فحص المكتبات:")
    for lib_name, import_name in required_libraries:
        if not check_library(lib_name, import_name):
            missing_libraries.append(lib_name)
    
    print("\n" + "=" * 60)
    
    # فحص ffmpeg
    print("🎬 فحص ffmpeg:")
    ffmpeg_available = check_ffmpeg()
    
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج:")
    
    if missing_libraries:
        print(f"❌ المكتبات المفقودة ({len(missing_libraries)}):")
        for lib in missing_libraries:
            print(f"   - {lib}")
        
        print(f"\n🔄 جاري تثبيت {len(missing_libraries)} مكتبة مفقودة...")
        success_count = 0
        for lib in missing_libraries:
            if install_library(lib):
                success_count += 1
        
        print(f"\n✅ تم تثبيت {success_count} من {len(missing_libraries)} مكتبة")
    else:
        print("✅ جميع المكتبات المطلوبة مثبتة")
    
    if not ffmpeg_available:
        print("\n⚠️  ffmpeg غير مثبت")
        if os.name == 'nt':  # Windows
            print("🔄 محاولة التثبيت التلقائي...")
            if not install_ffmpeg_windows():
                print("\n📋 طرق تثبيت ffmpeg يدوياً على Windows:")
                print("1. winget install ffmpeg")
                print("2. choco install ffmpeg")
                print("3. conda install ffmpeg")
                print("4. تحميل من: https://ffmpeg.org/download.html")
        else:
            print("📋 لتثبيت ffmpeg:")
            print("sudo apt-get install ffmpeg  # Ubuntu/Debian")
            print("brew install ffmpeg          # macOS")
    else:
        print("✅ ffmpeg متوفر ويعمل بشكل صحيح")
    
    print("\n" + "=" * 60)
    print("🎯 انتهى فحص وتثبيت المكتبات")
    
    # فحص نهائي
    print("\n🔍 فحص نهائي سريع...")
    try:
        import PIL, moviepy, numpy, matplotlib
        print("✅ المكتبات الأساسية تعمل بشكل صحيح")
    except ImportError as e:
        print(f"❌ مشكلة في المكتبات الأساسية: {e}")

if __name__ == "__main__":
    main()
