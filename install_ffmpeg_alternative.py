#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import sys
import os
import urllib.request
import zipfile
import shutil

def check_ffmpeg():
    """فحص وجود ffmpeg"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ ffmpeg مثبت ويعمل")
            version_line = result.stdout.split('\n')[0]
            print(f"   الإصدار: {version_line}")
            return True
        else:
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False

def install_ffmpeg_portable():
    """تثبيت ffmpeg المحمول"""
    print("🔄 تحميل ffmpeg المحمول...")
    
    # رابط تحميل ffmpeg المحمول
    ffmpeg_url = "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip"
    
    try:
        # إنشاء مجلد ffmpeg
        ffmpeg_dir = os.path.join(os.getcwd(), "ffmpeg")
        if not os.path.exists(ffmpeg_dir):
            os.makedirs(ffmpeg_dir)
        
        zip_path = os.path.join(ffmpeg_dir, "ffmpeg.zip")
        
        print("📥 جاري تحميل ffmpeg...")
        urllib.request.urlretrieve(ffmpeg_url, zip_path)
        
        print("📦 جاري استخراج الملفات...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(ffmpeg_dir)
        
        # البحث عن ملف ffmpeg.exe
        for root, dirs, files in os.walk(ffmpeg_dir):
            if 'ffmpeg.exe' in files:
                ffmpeg_exe = os.path.join(root, 'ffmpeg.exe')
                print(f"✅ تم العثور على ffmpeg في: {ffmpeg_exe}")
                
                # إضافة المسار إلى PATH
                bin_dir = os.path.dirname(ffmpeg_exe)
                current_path = os.environ.get('PATH', '')
                if bin_dir not in current_path:
                    os.environ['PATH'] = bin_dir + os.pathsep + current_path
                    print(f"✅ تم إضافة {bin_dir} إلى PATH")
                
                # حذف ملف الـ zip
                os.remove(zip_path)
                return True
        
        print("❌ لم يتم العثور على ffmpeg.exe في الملفات المستخرجة")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في تحميل ffmpeg: {e}")
        return False

def install_via_conda():
    """محاولة التثبيت عبر conda"""
    try:
        print("🔄 محاولة التثبيت عبر conda...")
        result = subprocess.run(['conda', 'install', '-c', 'conda-forge', 'ffmpeg', '-y'], 
                              capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print("✅ تم تثبيت ffmpeg عبر conda")
            return True
        else:
            print("❌ فشل التثبيت عبر conda")
            return False
    except:
        print("❌ conda غير متوفر")
        return False

def install_via_pip():
    """محاولة التثبيت عبر pip (ffmpeg-python)"""
    try:
        print("🔄 تثبيت ffmpeg-python...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'ffmpeg-python'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ تم تثبيت ffmpeg-python")
            return True
        else:
            print("❌ فشل تثبيت ffmpeg-python")
            return False
    except Exception as e:
        print(f"❌ خطأ في تثبيت ffmpeg-python: {e}")
        return False

def main():
    print("🎬 فحص وتثبيت ffmpeg")
    print("=" * 50)
    
    # فحص أولي
    if check_ffmpeg():
        print("✅ ffmpeg متوفر بالفعل!")
        return
    
    print("❌ ffmpeg غير متوفر، جاري المحاولة للتثبيت...")
    
    # محاولة التثبيت عبر conda
    if install_via_conda():
        if check_ffmpeg():
            print("✅ تم تثبيت ffmpeg بنجاح عبر conda!")
            return
    
    # محاولة تثبيت النسخة المحمولة
    if install_ffmpeg_portable():
        if check_ffmpeg():
            print("✅ تم تثبيت ffmpeg المحمول بنجاح!")
            return
    
    # تثبيت ffmpeg-python كبديل
    install_via_pip()
    
    print("\n" + "=" * 50)
    print("📋 إذا لم ينجح التثبيت التلقائي، يمكنك:")
    print("1. تحميل ffmpeg من: https://ffmpeg.org/download.html")
    print("2. استخدام: choco install ffmpeg")
    print("3. استخدام: winget install ffmpeg")
    print("4. استخدام: conda install ffmpeg")
    
    # فحص نهائي
    if check_ffmpeg():
        print("\n✅ ffmpeg يعمل الآن!")
    else:
        print("\n❌ ffmpeg لا يزال غير متوفر")

if __name__ == "__main__":
    main()
