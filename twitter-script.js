// بيانات التغريدات (فارغة في البداية)
const tweetsData = [];

// إعدادات التخصيص الافتراضية
const defaultCustomization = {
    backgroundColor: 'black',
    fontSize: 'medium',
    hasBorder: false,
    avatarShape: 'circle'
};

// تحميل الإعدادات المحفوظة أو استخدام الافتراضية
let savedCustomization = JSON.parse(localStorage.getItem('tweetCustomization')) || defaultCustomization;

// تحميل التغريدات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadTweets();
    setupComposeArea();
});

// تحميل التغريدات
function loadTweets() {
    const container = document.getElementById('tweetsContainer');
    container.innerHTML = '';
    
    tweetsData.forEach(tweet => {
        const tweetElement = createTweetElement(tweet);
        container.appendChild(tweetElement);
    });
}

// إنشاء عنصر التغريدة
function createTweetElement(tweet) {
    const tweetDiv = document.createElement('div');
    tweetDiv.className = 'tweet';
    tweetDiv.id = `tweet-${tweet.id}`;

    // تطبيق إعدادات التخصيص
    applyCustomization(tweetDiv);

    tweetDiv.innerHTML = `
        <div class="tweet-header">
            <img src="${tweet.avatar}" alt="${tweet.name}" class="tweet-avatar ${savedCustomization.avatarShape === 'square' ? 'square-avatar' : ''}">
            <div class="tweet-user-info">
                <div class="tweet-user-name">
                    <span class="user-name">${tweet.name}</span>
                    ${tweet.verified ? '<i class="fas fa-check-circle verified-badge"></i>' : ''}
                    <span class="user-handle">${tweet.username}</span>
                    <span class="tweet-time">· ${tweet.time}</span>
                </div>
            </div>
            <div class="tweet-options">
                <button class="options-btn" onclick="toggleCustomizationMenu(${tweet.id})">
                    <i class="fas fa-cog"></i>
                </button>
                <div id="customization-menu-${tweet.id}" class="customization-menu hidden">
                    <div class="customization-section">
                        <label>لون الخلفية:</label>
                        <div class="color-options">
                            <button class="color-btn black ${savedCustomization.backgroundColor === 'black' ? 'active' : ''}" onclick="changeBackgroundColor(${tweet.id}, 'black')"></button>
                            <button class="color-btn white ${savedCustomization.backgroundColor === 'white' ? 'active' : ''}" onclick="changeBackgroundColor(${tweet.id}, 'white')"></button>
                            <button class="color-btn dark-blue ${savedCustomization.backgroundColor === 'dark-blue' ? 'active' : ''}" onclick="changeBackgroundColor(${tweet.id}, 'dark-blue')"></button>
                        </div>
                    </div>
                    <div class="customization-section">
                        <label>حجم الخط:</label>
                        <div class="font-size-options">
                            <button class="size-btn ${savedCustomization.fontSize === 'small' ? 'active' : ''}" onclick="changeFontSize(${tweet.id}, 'small')">صغير</button>
                            <button class="size-btn ${savedCustomization.fontSize === 'medium' ? 'active' : ''}" onclick="changeFontSize(${tweet.id}, 'medium')">متوسط</button>
                            <button class="size-btn ${savedCustomization.fontSize === 'large' ? 'active' : ''}" onclick="changeFontSize(${tweet.id}, 'large')">كبير</button>
                        </div>
                    </div>
                    <div class="customization-section">
                        <label class="checkbox-label">
                            <input type="checkbox" ${savedCustomization.hasBorder ? 'checked' : ''} onchange="toggleBorder(${tweet.id}, this.checked)">
                            إضافة حدود
                        </label>
                    </div>
                    <div class="customization-section">
                        <label>شكل الصورة الشخصية:</label>
                        <div class="avatar-shape-options">
                            <button class="shape-btn ${savedCustomization.avatarShape === 'circle' ? 'active' : ''}" onclick="changeAvatarShape(${tweet.id}, 'circle')">دائرية</button>
                            <button class="shape-btn ${savedCustomization.avatarShape === 'square' ? 'active' : ''}" onclick="changeAvatarShape(${tweet.id}, 'square')">مربعة</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tweet-content">${tweet.content}</div>
        ${tweet.image ? `<div class="tweet-media"><img src="${tweet.image}" alt="صورة التغريدة"></div>` : ''}
        <div class="tweet-actions">
            <button class="action-btn reply-btn" onclick="replyToTweet(${tweet.id})">
                <i class="far fa-comment"></i>
                <span>${formatNumber(tweet.replies)}</span>
            </button>
            <button class="action-btn retweet-btn" onclick="retweetTweet(${tweet.id})">
                <i class="fas fa-retweet"></i>
                <span>${formatNumber(tweet.retweets)}</span>
            </button>
            <button class="action-btn like-btn" onclick="likeTweet(${tweet.id})">
                <i class="far fa-heart"></i>
                <span>${formatNumber(tweet.likes)}</span>
            </button>
            <button class="action-btn share-btn" onclick="shareTweet(${tweet.id})">
                <i class="fas fa-share"></i>
            </button>
            <button class="action-btn download-btn" onclick="downloadTweetAsImage(${tweet.id})">
                <i class="fas fa-download"></i>
                <span>تحميل</span>
            </button>
        </div>
    `;

    return tweetDiv;
}

// تنسيق الأرقام
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// إعداد منطقة كتابة التغريدة
function setupComposeArea() {
    const composeInput = document.querySelector('.compose-input');
    const tweetBtn = document.querySelector('.tweet-btn');
    
    composeInput.addEventListener('input', function() {
        const text = this.value.trim();
        tweetBtn.disabled = text.length === 0 || text.length > 280;
        
        if (text.length > 280) {
            this.style.borderColor = '#f91880';
        } else {
            this.style.borderColor = 'transparent';
        }
    });
    
    tweetBtn.addEventListener('click', function() {
        const text = composeInput.value.trim();
        if (text && text.length <= 280) {
            addNewTweet(text);
            composeInput.value = '';
            tweetBtn.disabled = true;
        }
    });
}

// إضافة تغريدة جديدة
function addNewTweet(content) {
    const newTweet = {
        id: Date.now(),
        name: "أنت",
        username: "@you",
        avatar: "https://via.placeholder.com/48x48/1DA1F2/ffffff?text=أ",
        verified: false,
        time: "الآن",
        content: content,
        replies: 0,
        retweets: 0,
        likes: 0
    };
    
    tweetsData.unshift(newTweet);
    
    const container = document.getElementById('tweetsContainer');
    const tweetElement = createTweetElement(newTweet);
    container.insertBefore(tweetElement, container.firstChild);
    
    // تأثير بصري للتغريدة الجديدة
    tweetElement.style.backgroundColor = 'rgba(29, 155, 240, 0.1)';
    setTimeout(() => {
        tweetElement.style.backgroundColor = '';
    }, 2000);
}

// وظائف التفاعل مع التغريدات
function replyToTweet(tweetId) {
    const tweet = tweetsData.find(t => t.id === tweetId);
    if (tweet) {
        const composeInput = document.querySelector('.compose-input');
        composeInput.value = `@${tweet.username.substring(1)} `;
        composeInput.focus();
        composeInput.setSelectionRange(composeInput.value.length, composeInput.value.length);
    }
}

function retweetTweet(tweetId) {
    const tweet = tweetsData.find(t => t.id === tweetId);
    const button = event.target.closest('.retweet-btn');
    
    if (tweet && button) {
        if (!button.classList.contains('retweeted')) {
            tweet.retweets++;
            button.classList.add('retweeted');
            button.querySelector('span').textContent = formatNumber(tweet.retweets);
            
            // تأثير بصري
            button.style.transform = 'scale(1.2)';
            setTimeout(() => {
                button.style.transform = '';
            }, 200);
        }
    }
}

function likeTweet(tweetId) {
    const tweet = tweetsData.find(t => t.id === tweetId);
    const button = event.target.closest('.like-btn');
    
    if (tweet && button) {
        if (!button.classList.contains('liked')) {
            tweet.likes++;
            button.classList.add('liked');
            button.querySelector('i').className = 'fas fa-heart';
            button.querySelector('span').textContent = formatNumber(tweet.likes);
            
            // تأثير بصري
            button.style.transform = 'scale(1.2)';
            setTimeout(() => {
                button.style.transform = '';
            }, 200);
        } else {
            tweet.likes--;
            button.classList.remove('liked');
            button.querySelector('i').className = 'far fa-heart';
            button.querySelector('span').textContent = formatNumber(tweet.likes);
        }
    }
}

function shareTweet(tweetId) {
    const tweet = tweetsData.find(t => t.id === tweetId);
    if (tweet) {
        if (navigator.share) {
            navigator.share({
                title: `تغريدة من ${tweet.name}`,
                text: tweet.content,
                url: window.location.href
            });
        } else {
            // نسخ الرابط إلى الحافظة
            navigator.clipboard.writeText(window.location.href).then(() => {
                alert('تم نسخ الرابط إلى الحافظة!');
            });
        }
    }
}

// وظائف التخصيص
function applyCustomization(tweetElement) {
    // تطبيق لون الخلفية
    tweetElement.classList.remove('bg-black', 'bg-white', 'bg-dark-blue');
    tweetElement.classList.add(`bg-${savedCustomization.backgroundColor}`);

    // تطبيق حجم الخط
    tweetElement.classList.remove('font-small', 'font-medium', 'font-large');
    tweetElement.classList.add(`font-${savedCustomization.fontSize}`);

    // تطبيق الحدود
    if (savedCustomization.hasBorder) {
        tweetElement.classList.add('has-border');
    } else {
        tweetElement.classList.remove('has-border');
    }
}

function toggleCustomizationMenu(tweetId) {
    const menu = document.getElementById(`customization-menu-${tweetId}`);
    const allMenus = document.querySelectorAll('.customization-menu');

    // إغلاق جميع القوائم الأخرى
    allMenus.forEach(m => {
        if (m !== menu) {
            m.classList.add('hidden');
        }
    });

    // تبديل القائمة الحالية
    menu.classList.toggle('hidden');
}

function changeBackgroundColor(tweetId, color) {
    savedCustomization.backgroundColor = color;
    saveCustomization();

    const tweetElement = document.getElementById(`tweet-${tweetId}`);
    applyCustomization(tweetElement);

    // تحديث الأزرار النشطة
    updateActiveButtons(tweetId);
}

function changeFontSize(tweetId, size) {
    savedCustomization.fontSize = size;
    saveCustomization();

    const tweetElement = document.getElementById(`tweet-${tweetId}`);
    applyCustomization(tweetElement);

    // تحديث الأزرار النشطة
    updateActiveButtons(tweetId);
}

function toggleBorder(tweetId, hasBorder) {
    savedCustomization.hasBorder = hasBorder;
    saveCustomization();

    const tweetElement = document.getElementById(`tweet-${tweetId}`);
    applyCustomization(tweetElement);
}

function changeAvatarShape(tweetId, shape) {
    savedCustomization.avatarShape = shape;
    saveCustomization();

    const tweetElement = document.getElementById(`tweet-${tweetId}`);
    const avatar = tweetElement.querySelector('.tweet-avatar');

    if (shape === 'square') {
        avatar.classList.add('square-avatar');
    } else {
        avatar.classList.remove('square-avatar');
    }

    // تحديث الأزرار النشطة
    updateActiveButtons(tweetId);
}

function updateActiveButtons(tweetId) {
    const menu = document.getElementById(`customization-menu-${tweetId}`);

    // تحديث أزرار الألوان
    menu.querySelectorAll('.color-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.classList.contains(savedCustomization.backgroundColor)) {
            btn.classList.add('active');
        }
    });

    // تحديث أزرار حجم الخط
    menu.querySelectorAll('.size-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    menu.querySelector(`.size-btn:nth-child(${savedCustomization.fontSize === 'small' ? '1' : savedCustomization.fontSize === 'medium' ? '2' : '3'})`).classList.add('active');

    // تحديث أزرار شكل الصورة
    menu.querySelectorAll('.shape-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    menu.querySelector(`.shape-btn:nth-child(${savedCustomization.avatarShape === 'circle' ? '1' : '2'})`).classList.add('active');
}

function saveCustomization() {
    localStorage.setItem('tweetCustomization', JSON.stringify(savedCustomization));
}

// تحميل التغريدة كصورة
async function downloadTweetAsImage(tweetId) {
    const tweetElement = document.getElementById(`tweet-${tweetId}`);
    const tweet = tweetsData.find(t => t.id === tweetId);

    if (!tweetElement || !tweet) return;

    // إخفاء قائمة التخصيص مؤقتاً
    const menu = document.getElementById(`customization-menu-${tweetId}`);
    const wasHidden = menu.classList.contains('hidden');
    menu.classList.add('hidden');

    // إخفاء زر التحميل مؤقتاً
    const downloadBtn = tweetElement.querySelector('.download-btn');
    downloadBtn.style.display = 'none';

    try {
        const canvas = await html2canvas(tweetElement, {
            backgroundColor: null,
            scale: 3,
            useCORS: true,
            allowTaint: true,
            width: tweetElement.offsetWidth,
            height: tweetElement.offsetHeight
        });

        canvas.toBlob(blob => {
            const link = document.createElement('a');
            link.download = `tweet-${tweet.name.replace(/\s+/g, '-')}-${Date.now()}.png`;
            link.href = URL.createObjectURL(blob);
            link.click();
            URL.revokeObjectURL(link.href);
        }, 'image/png', 1.0);

    } catch (error) {
        console.error('خطأ في تحميل الصورة:', error);
        alert('حدث خطأ أثناء تحميل الصورة. يرجى المحاولة مرة أخرى.');
    } finally {
        // إعادة إظهار العناصر
        downloadBtn.style.display = '';
        if (!wasHidden) {
            menu.classList.remove('hidden');
        }
    }
}
