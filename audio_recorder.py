#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسجل الصوت للتعليقات الصوتية
Audio Recorder for Voice Comments
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import wave
import time
import os
from datetime import datetime

try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
except ImportError:
    PYAUDIO_AVAILABLE = False

class AudioRecorder:
    def __init__(self, parent=None):
        self.parent = parent
        self.is_recording = False
        self.audio_data = []
        self.audio_stream = None
        self.audio_instance = None
        
        # إعدادات التسجيل
        self.CHUNK = 1024
        self.FORMAT = pyaudio.paInt16 if PYAUDIO_AVAILABLE else None
        self.CHANNELS = 1
        self.RATE = 44100
        
        self.setup_recorder_window()
    
    def setup_recorder_window(self):
        """إعداد نافذة المسجل"""
        self.recorder_window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.recorder_window.title("🎤 مسجل الصوت - Audio Recorder")
        self.recorder_window.geometry("400x300")
        self.recorder_window.configure(bg='#34495e')
        
        # إطار رئيسي
        main_frame = ttk.Frame(self.recorder_window, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان
        title_label = ttk.Label(main_frame, text="🎤 تسجيل التعليق الصوتي", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # حالة التسجيل
        self.status_var = tk.StringVar(value="جاهز للتسجيل")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, 
                                font=('Arial', 12))
        status_label.pack(pady=(0, 10))
        
        # مؤشر التسجيل
        self.recording_indicator = tk.Canvas(main_frame, width=20, height=20, bg='#34495e')
        self.recording_indicator.pack(pady=(0, 20))
        
        # أزرار التحكم
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.record_button = ttk.Button(button_frame, text="🔴 بدء التسجيل", 
                                       command=self.start_recording)
        self.record_button.pack(side=tk.LEFT, padx=(0, 10), fill=tk.X, expand=True)
        
        self.stop_button = ttk.Button(button_frame, text="⏹️ إيقاف التسجيل", 
                                     command=self.stop_recording, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(10, 0), fill=tk.X, expand=True)
        
        # أزرار إضافية
        action_frame = ttk.Frame(main_frame)
        action_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Button(action_frame, text="▶️ تشغيل", command=self.play_recording).pack(side=tk.LEFT, padx=(0, 5), fill=tk.X, expand=True)
        ttk.Button(action_frame, text="💾 حفظ", command=self.save_recording).pack(side=tk.LEFT, padx=(5, 5), fill=tk.X, expand=True)
        ttk.Button(action_frame, text="🗑️ حذف", command=self.clear_recording).pack(side=tk.LEFT, padx=(5, 0), fill=tk.X, expand=True)
        
        # معلومات التسجيل
        info_frame = ttk.LabelFrame(main_frame, text="معلومات التسجيل", padding=10)
        info_frame.pack(fill=tk.X)
        
        self.duration_var = tk.StringVar(value="المدة: 0:00")
        ttk.Label(info_frame, textvariable=self.duration_var).pack(anchor=tk.W)
        
        self.size_var = tk.StringVar(value="الحجم: 0 KB")
        ttk.Label(info_frame, textvariable=self.size_var).pack(anchor=tk.W)
        
        # التحقق من توفر pyaudio
        if not PYAUDIO_AVAILABLE:
            self.status_var.set("❌ مكتبة pyaudio غير مثبتة")
            self.record_button.configure(state=tk.DISABLED)
            messagebox.showwarning("تحذير", 
                                 "مكتبة pyaudio غير مثبتة.\nيرجى تثبيتها لاستخدام ميزة التسجيل:\npip install pyaudio")
        
        # متغيرات التسجيل
        self.recorded_file = None
        self.recording_start_time = None
        
        # تحديث المؤشر
        self.update_indicator()
    
    def update_indicator(self):
        """تحديث مؤشر التسجيل"""
        self.recording_indicator.delete("all")
        
        if self.is_recording:
            # دائرة حمراء وامضة
            color = "#e74c3c" if int(time.time() * 2) % 2 else "#c0392b"
            self.recording_indicator.create_oval(2, 2, 18, 18, fill=color, outline=color)
        else:
            # دائرة رمادية
            self.recording_indicator.create_oval(2, 2, 18, 18, fill="#95a5a6", outline="#95a5a6")
        
        # جدولة التحديث التالي
        self.recorder_window.after(500, self.update_indicator)
    
    def start_recording(self):
        """بدء التسجيل"""
        if not PYAUDIO_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة pyaudio غير متوفرة")
            return
        
        try:
            self.audio_instance = pyaudio.PyAudio()
            
            # فتح stream للتسجيل
            self.audio_stream = self.audio_instance.open(
                format=self.FORMAT,
                channels=self.CHANNELS,
                rate=self.RATE,
                input=True,
                frames_per_buffer=self.CHUNK
            )
            
            self.is_recording = True
            self.audio_data = []
            self.recording_start_time = time.time()
            
            # تحديث الواجهة
            self.status_var.set("🔴 جاري التسجيل...")
            self.record_button.configure(state=tk.DISABLED)
            self.stop_button.configure(state=tk.NORMAL)
            
            # بدء thread للتسجيل
            self.recording_thread = threading.Thread(target=self.record_audio)
            self.recording_thread.daemon = True
            self.recording_thread.start()
            
            # بدء تحديث المدة
            self.update_duration()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في بدء التسجيل: {str(e)}")
            self.is_recording = False
    
    def record_audio(self):
        """تسجيل الصوت في thread منفصل"""
        try:
            while self.is_recording:
                data = self.audio_stream.read(self.CHUNK)
                self.audio_data.append(data)
        except Exception as e:
            print(f"خطأ في التسجيل: {e}")
    
    def stop_recording(self):
        """إيقاف التسجيل"""
        self.is_recording = False
        
        if self.audio_stream:
            self.audio_stream.stop_stream()
            self.audio_stream.close()
        
        if self.audio_instance:
            self.audio_instance.terminate()
        
        # تحديث الواجهة
        self.status_var.set("✅ تم إنهاء التسجيل")
        self.record_button.configure(state=tk.NORMAL)
        self.stop_button.configure(state=tk.DISABLED)
        
        # حفظ التسجيل مؤقتاً
        self.save_temp_recording()
    
    def save_temp_recording(self):
        """حفظ التسجيل في ملف مؤقت"""
        if not self.audio_data:
            return
        
        try:
            # إنشاء ملف مؤقت
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_filename = f"temp_recording_{timestamp}.wav"
            
            # حفظ الملف
            with wave.open(temp_filename, 'wb') as wf:
                wf.setnchannels(self.CHANNELS)
                wf.setsampwidth(self.audio_instance.get_sample_size(self.FORMAT) if self.audio_instance else 2)
                wf.setframerate(self.RATE)
                wf.writeframes(b''.join(self.audio_data))
            
            self.recorded_file = temp_filename
            
            # تحديث معلومات الملف
            file_size = os.path.getsize(temp_filename) / 1024  # KB
            self.size_var.set(f"الحجم: {file_size:.1f} KB")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ التسجيل: {str(e)}")
    
    def update_duration(self):
        """تحديث مدة التسجيل"""
        if self.is_recording and self.recording_start_time:
            duration = time.time() - self.recording_start_time
            minutes = int(duration // 60)
            seconds = int(duration % 60)
            self.duration_var.set(f"المدة: {minutes}:{seconds:02d}")
            
            # جدولة التحديث التالي
            self.recorder_window.after(1000, self.update_duration)
    
    def play_recording(self):
        """تشغيل التسجيل"""
        if not self.recorded_file or not os.path.exists(self.recorded_file):
            messagebox.showwarning("تحذير", "لا يوجد تسجيل للتشغيل")
            return
        
        try:
            # استخدام pygame لتشغيل الصوت
            import pygame
            pygame.mixer.init()
            pygame.mixer.music.load(self.recorded_file)
            pygame.mixer.music.play()
            
            self.status_var.set("▶️ جاري التشغيل...")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تشغيل التسجيل: {str(e)}")
    
    def save_recording(self):
        """حفظ التسجيل في مكان محدد"""
        if not self.recorded_file or not os.path.exists(self.recorded_file):
            messagebox.showwarning("تحذير", "لا يوجد تسجيل للحفظ")
            return
        
        # اختيار مكان الحفظ
        save_path = filedialog.asksaveasfilename(
            title="حفظ التسجيل الصوتي",
            defaultextension=".wav",
            filetypes=[
                ("ملفات WAV", "*.wav"),
                ("ملفات MP3", "*.mp3"),
                ("جميع الملفات", "*.*")
            ]
        )
        
        if save_path:
            try:
                # نسخ الملف
                import shutil
                shutil.copy2(self.recorded_file, save_path)
                
                self.status_var.set(f"✅ تم الحفظ: {os.path.basename(save_path)}")
                messagebox.showinfo("نجح", f"تم حفظ التسجيل بنجاح!\n{save_path}")
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ التسجيل: {str(e)}")
    
    def clear_recording(self):
        """حذف التسجيل الحالي"""
        if self.recorded_file and os.path.exists(self.recorded_file):
            try:
                os.remove(self.recorded_file)
            except:
                pass
        
        self.recorded_file = None
        self.audio_data = []
        self.status_var.set("جاهز للتسجيل")
        self.duration_var.set("المدة: 0:00")
        self.size_var.set("الحجم: 0 KB")
    
    def get_recorded_file(self):
        """الحصول على مسار الملف المسجل"""
        return self.recorded_file
    
    def close(self):
        """إغلاق المسجل وتنظيف الملفات المؤقتة"""
        if self.is_recording:
            self.stop_recording()
        
        # حذف الملفات المؤقتة
        if self.recorded_file and os.path.exists(self.recorded_file):
            try:
                os.remove(self.recorded_file)
            except:
                pass
        
        if hasattr(self, 'recorder_window'):
            self.recorder_window.destroy()

def main():
    """تشغيل المسجل كبرنامج مستقل"""
    recorder = AudioRecorder()
    recorder.recorder_window.mainloop()

if __name__ == "__main__":
    main()
