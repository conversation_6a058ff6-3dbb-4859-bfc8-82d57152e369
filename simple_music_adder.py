#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة موسيقى خلفية بسيطة للفيديو
Add simple background music to video
"""

import os
import numpy as np
from moviepy.editor import VideoFileClip, CompositeAudioClip
from moviepy.audio.AudioClip import AudioClip
import math

def generate_epic_music(duration=25, sample_rate=22050):
    """Generate epic background music"""
    print("إنشاء موسيقى خلفية...")
    print("Creating background music...")
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # Create epic chord progression
    frequencies = [110, 123.47, 138.59, 164.81, 185, 220]  # A minor pentatonic
    
    music = np.zeros_like(t)
    
    # Add multiple layers
    for i, freq in enumerate(frequencies):
        # Base tone
        wave = np.sin(2 * np.pi * freq * t)
        
        # Add harmonics
        wave += 0.3 * np.sin(2 * np.pi * freq * 2 * t)
        wave += 0.2 * np.sin(2 * np.pi * freq * 1.5 * t)
        
        # Apply envelope for each layer
        envelope = 0.5 + 0.5 * np.sin(2 * np.pi * t / (duration / (i + 1)))
        wave *= envelope
        
        # Add to music with different weights
        weight = 1.0 / (i + 1)
        music += weight * wave
    
    # Add rhythm/percussion
    beat_freq = 2  # 2 beats per second
    for beat_time in np.arange(0, duration, 1.0/beat_freq):
        beat_start = int(beat_time * sample_rate)
        beat_length = int(0.1 * sample_rate)
        
        if beat_start + beat_length < len(music):
            # Create kick drum effect
            kick_t = np.linspace(0, 0.1, beat_length)
            kick = 0.5 * np.sin(2 * np.pi * 60 * kick_t) * np.exp(-kick_t * 15)
            music[beat_start:beat_start + beat_length] += kick
    
    # Normalize
    music = music / np.max(np.abs(music)) * 0.6
    
    # Convert to stereo
    stereo_music = np.array([music, music]).T
    
    return stereo_music

def add_music_to_video(input_video, output_video):
    """Add background music to video"""
    print(f"معالجة الفيديو: {input_video}")
    print(f"Processing video: {input_video}")
    
    try:
        # Load video
        video = VideoFileClip(input_video)
        duration = video.duration
        
        # Generate music
        music_array = generate_epic_music(duration)
        
        # Create audio clip
        def make_frame_audio(t):
            frame_idx = int(t * 22050)
            if frame_idx < len(music_array):
                return music_array[frame_idx]
            else:
                return np.array([0.0, 0.0])

        background_music = AudioClip(make_frame_audio, duration=duration)
        
        # Set volume
        background_music = background_music.volumex(0.4)
        
        # Combine audio
        if video.audio is not None:
            final_audio = CompositeAudioClip([video.audio, background_music])
        else:
            final_audio = background_music
        
        # Create final video
        final_video = video.set_audio(final_audio)
        
        # Export
        print(f"حفظ الفيديو النهائي: {output_video}")
        print(f"Saving final video: {output_video}")
        
        final_video.write_videofile(
            output_video,
            fps=30,
            codec='libx264',
            audio_codec='aac',
            preset='medium',
            verbose=False,
            logger=None
        )
        
        # Cleanup
        video.close()
        final_video.close()
        background_music.close()
        
        return True
        
    except Exception as e:
        print(f"خطأ: {e}")
        print(f"Error: {e}")
        return False

def main():
    """Main function"""
    input_file = "enhanced_glowing_flags_tiktok.mp4"
    output_file = "final_glowing_flags_with_music.mp4"
    
    print("=" * 60)
    print("🎵 إضافة موسيقى خلفية حماسية للفيديو")
    print("🎵 Adding Epic Background Music to Video")
    print("=" * 60)
    
    if not os.path.exists(input_file):
        print(f"❌ الفيديو غير موجود: {input_file}")
        print(f"❌ Video not found: {input_file}")
        return
    
    success = add_music_to_video(input_file, output_file)
    
    if success:
        print("\n" + "=" * 60)
        print("✅ تم إنشاء الفيديو النهائي بنجاح!")
        print("✅ Final video created successfully!")
        print(f"📁 الملف: {output_file}")
        print(f"📁 File: {output_file}")
        
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file) / (1024 * 1024)
            print(f"📊 حجم الملف: {file_size:.2f} MB")
            print(f"📊 File size: {file_size:.2f} MB")
        
        print("\n🎯 المميزات النهائية:")
        print("🎯 Final features:")
        print("   • دائرة مضيئة متحركة / Animated glowing circle")
        print("   • أعلام متحركة (الصين، روسيا، تركيا ضد أمريكا)")
        print("   • Animated flags (China, Russia, Turkey vs USA)")
        print("   • نص مضيء 'Which country will win?'")
        print("   • Glowing text 'Which country will win?'")
        print("   • موسيقى خلفية حماسية / Epic background music")
        print("   • تأثيرات بصرية متقدمة / Advanced visual effects")
        print("   • صيغة TikTok/YouTube Shorts (9:16)")
        print("   • مدة 25 ثانية / 25 seconds duration")
        print("=" * 60)
        
    else:
        print("❌ فشل في إنشاء الفيديو")
        print("❌ Failed to create video")

if __name__ == "__main__":
    main()
