/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #000;
    color: #fff;
    line-height: 1.6;
    direction: rtl;
}

.container {
    max-width: 600px;
    margin: 0 auto;
    padding: 0 20px;
}

/* الشريط العلوي */
.header {
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(12px);
    border-bottom: 1px solid #2f3336;
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 12px 0;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #1d9bf0;
}

.logo i {
    font-size: 28px;
}

.logo h1 {
    font-size: 24px;
    font-weight: 700;
}

.nav {
    display: flex;
    gap: 32px;
}

.nav-link {
    color: #e7e9ea;
    text-decoration: none;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 20px;
    transition: all 0.2s ease;
}

.nav-link:hover {
    background-color: rgba(29, 155, 240, 0.1);
    color: #1d9bf0;
}

.nav-link.active {
    color: #1d9bf0;
    font-weight: 700;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: opacity 0.2s ease;
}

.user-avatar:hover {
    opacity: 0.8;
}

/* المحتوى الرئيسي */
.main {
    padding: 20px 0;
    min-height: calc(100vh - 80px);
}

/* منطقة كتابة التغريدة */
.compose-tweet {
    background-color: #000;
    border: 1px solid #2f3336;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
}

.compose-header {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.compose-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    flex-shrink: 0;
}

.compose-input {
    flex: 1;
    background: transparent;
    border: none;
    color: #fff;
    font-size: 20px;
    font-family: inherit;
    resize: none;
    min-height: 60px;
    outline: none;
}

.compose-input::placeholder {
    color: #71767b;
}

.compose-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16px;
    border-top: 1px solid #2f3336;
}

.compose-icons {
    display: flex;
    gap: 16px;
}

.compose-icons i {
    color: #1d9bf0;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.compose-icons i:hover {
    background-color: rgba(29, 155, 240, 0.1);
}

.tweet-btn {
    background-color: #1d9bf0;
    color: #fff;
    border: none;
    padding: 8px 24px;
    border-radius: 20px;
    font-weight: 700;
    font-size: 15px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.tweet-btn:hover {
    background-color: #1a8cd8;
}

.tweet-btn:disabled {
    background-color: #0f4e78;
    cursor: not-allowed;
}

/* التغريدات */
.tweet {
    background-color: #000;
    border-bottom: 1px solid #2f3336;
    padding: 16px 20px;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.tweet:hover {
    background-color: rgba(255, 255, 255, 0.03);
}

.tweet-header {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
}

.tweet-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    flex-shrink: 0;
}

.tweet-user-info {
    flex: 1;
}

.tweet-user-name {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 2px;
}

.user-name {
    color: #e7e9ea;
    font-weight: 700;
    font-size: 15px;
}

.verified-badge {
    color: #1d9bf0;
    font-size: 16px;
}

.user-handle {
    color: #71767b;
    font-size: 15px;
}

.tweet-time {
    color: #71767b;
    font-size: 15px;
}

.tweet-content {
    color: #e7e9ea;
    font-size: 15px;
    line-height: 1.5;
    margin-bottom: 12px;
    word-wrap: break-word;
}

.tweet-media {
    margin-bottom: 12px;
    border-radius: 16px;
    overflow: hidden;
}

.tweet-media img {
    width: 100%;
    height: auto;
    display: block;
}

.tweet-actions {
    display: flex;
    justify-content: space-between;
    max-width: 425px;
    margin-top: 12px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #71767b;
    background: none;
    border: none;
    padding: 8px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 13px;
}

.action-btn:hover {
    background-color: rgba(29, 155, 240, 0.1);
    color: #1d9bf0;
}

.action-btn.liked {
    color: #f91880;
}

.action-btn.liked:hover {
    background-color: rgba(249, 24, 128, 0.1);
}

.action-btn.retweeted {
    color: #00ba7c;
}

.action-btn.retweeted:hover {
    background-color: rgba(0, 186, 124, 0.1);
}

.action-btn i {
    font-size: 18px;
}

/* حالة فارغة */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #71767b;
}

.empty-state i {
    font-size: 64px;
    color: #1d9bf0;
    margin-bottom: 20px;
}

.empty-state h3 {
    font-size: 24px;
    margin-bottom: 8px;
    color: #e7e9ea;
}

.empty-state p {
    font-size: 16px;
}

/* أزرار التخصيص */
.tweet-options {
    position: relative;
    margin-right: auto;
}

.options-btn {
    background: none;
    border: none;
    color: #71767b;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
}

.options-btn:hover {
    background-color: rgba(29, 155, 240, 0.1);
    color: #1d9bf0;
}

.customization-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: #000;
    border: 1px solid #2f3336;
    border-radius: 12px;
    padding: 16px;
    min-width: 250px;
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.customization-menu.hidden {
    display: none;
}

.customization-section {
    margin-bottom: 16px;
}

.customization-section:last-child {
    margin-bottom: 0;
}

.customization-section label {
    display: block;
    color: #e7e9ea;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
}

.color-options {
    display: flex;
    gap: 8px;
}

.color-btn {
    width: 32px;
    height: 32px;
    border: 2px solid transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.color-btn.black {
    background-color: #000;
    border-color: #2f3336;
}

.color-btn.white {
    background-color: #fff;
    border-color: #ccc;
}

.color-btn.dark-blue {
    background-color: #15202b;
    border-color: #1d9bf0;
}

.color-btn.active {
    border-color: #1d9bf0;
    box-shadow: 0 0 0 2px rgba(29, 155, 240, 0.2);
}

.font-size-options,
.avatar-shape-options {
    display: flex;
    gap: 8px;
}

.font-btn,
.shape-btn {
    background: #2f3336;
    color: #e7e9ea;
    border: 1px solid #2f3336;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.font-btn:hover,
.shape-btn:hover {
    background: #1d9bf0;
    border-color: #1d9bf0;
}

.font-btn.active,
.shape-btn.active {
    background: #1d9bf0;
    border-color: #1d9bf0;
}

/* أنماط التخصيص للتغريدات */
.tweet.bg-black {
    background-color: #000;
    color: #e7e9ea;
}

.tweet.bg-white {
    background-color: #fff;
    color: #0f1419;
}

.tweet.bg-white .user-name,
.tweet.bg-white .tweet-content {
    color: #0f1419;
}

.tweet.bg-white .user-handle,
.tweet.bg-white .tweet-time {
    color: #536471;
}

.tweet.bg-white .action-btn {
    color: #536471;
}

.tweet.bg-dark-blue {
    background-color: #15202b;
    color: #e7e9ea;
}

.tweet.font-small .tweet-content {
    font-size: 13px;
}

.tweet.font-medium .tweet-content {
    font-size: 15px;
}

.tweet.font-large .tweet-content {
    font-size: 18px;
}

.tweet.with-border {
    border: 2px solid #1d9bf0;
    border-radius: 12px;
}

.square-avatar {
    border-radius: 8px !important;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .container {
        padding: 0 16px;
    }
    
    .header .container {
        padding: 0 16px;
    }
    
    .nav {
        display: none;
    }
    
    .logo h1 {
        font-size: 20px;
    }
    
    .compose-tweet {
        border-radius: 0;
        border-left: none;
        border-right: none;
    }
    
    .tweet {
        padding: 12px 16px;
    }
    
    .tweet-actions {
        max-width: 100%;
    }
}

@media (max-width: 480px) {
    .compose-input {
        font-size: 18px;
    }
    
    .tweet-content {
        font-size: 14px;
    }
    
    .action-btn {
        padding: 6px;
        font-size: 12px;
    }
    
    .action-btn i {
        font-size: 16px;
    }
}
