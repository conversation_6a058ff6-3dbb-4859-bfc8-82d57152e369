# 🎬 محرر الفيديوهات مع التعليقات والعلامة المائية

## 📋 نظرة عامة
برنامج شامل لتحرير الفيديوهات يتيح لك:
- رفع الفيديوهات ومعاينتها
- إضافة التعليقات النصية والصوتية
- إنشاء علامة مائية مخصصة
- تشغيل الفيديو بشكل متكرر
- تصدير الفيديو النهائي بجودة عالية

## 🚀 كيفية التشغيل

### الطريقة السريعة:
```bash
python install_and_run.py
```

### الطريقة اليدوية:
1. تثبيت المكتبات المطلوبة:
```bash
pip install -r requirements.txt
```

2. تشغيل البرنامج:
```bash
python video_editor_with_comments.py
```

## 📦 المتطلبات

### المكتبات الأساسية:
- `moviepy` - معالجة الفيديو
- `opencv-python` - عرض الفيديو
- `pillow` - معالجة الصور
- `numpy` - العمليات الرياضية
- `pygame` - التشغيل
- `tkinter` - واجهة المستخدم (مدمجة مع Python)

### المكتبات الاختيارية:
- `pyaudio` - التسجيل الصوتي

### برامج خارجية:
- `FFmpeg` - ضروري لمعالجة الفيديو (يمكن تنزيله من https://ffmpeg.org)

## 🎯 الميزات الرئيسية

### 1. رفع ومعاينة الفيديو
- دعم صيغ متعددة: MP4, AVI, MOV, MKV, WMV, FLV
- معاينة فورية للفيديو
- عرض معلومات الفيديو (المدة، معدل الإطارات)

### 2. التحكم في التشغيل
- ▶️ تشغيل/إيقاف الفيديو
- 🔄 تشغيل متكرر (Loop)
- عرض الفيديو في الوقت الفعلي

### 3. إضافة التعليقات

#### التعليق النصي:
- إضافة نص يظهر على الفيديو
- تخصيص حجم ولون الخط
- اختيار موقع النص

#### التعليق الصوتي:
- رفع ملف صوتي (MP3, WAV, AAC, OGG)
- دمج الصوت مع الفيديو الأصلي
- تحكم في مستوى الصوت

### 4. العلامة المائية المخصصة

#### العناصر:
- 📷 صورة شخصية (دائرية)
- 👤 اسم المستخدم
- 🆔 معرف الحساب

#### التخصيص:
- اختيار موقع العلامة (4 مواقع)
- تحكم في الشفافية والحجم
- تصميم احترافي مع خلفية شبه شفافة

### 5. التصدير والحفظ
- تصدير بصيغة MP4 عالية الجودة
- ضغط محسن للمشاركة
- حفظ الإعدادات للاستخدام المستقبلي

## 🎮 كيفية الاستخدام

### الخطوة 1: رفع الفيديو
1. اضغط على "📁 رفع فيديو"
2. اختر ملف الفيديو من جهازك
3. ستظهر معاينة الفيديو في النافذة

### الخطوة 2: إعداد التعليق
#### للتعليق النصي:
1. اكتب النص في مربع "نص التعليق"
2. سيظهر النص في أسفل الفيديو

#### للتعليق الصوتي:
1. اضغط "📁 رفع ملف صوتي"
2. اختر ملف صوتي من جهازك
3. سيتم دمج الصوت مع الفيديو

### الخطوة 3: إعداد العلامة المائية
1. أدخل اسم المستخدم في الحقل المخصص
2. أدخل معرف الحساب (مثل @username)
3. اضغط "📷 رفع صورة شخصية" لإضافة صورتك
4. اختر موقع العلامة من القائمة المنسدلة

### الخطوة 4: معاينة وتشغيل
1. اضغط "▶️ تشغيل" لمعاينة الفيديو
2. اضغط "🔄 تكرار" لتفعيل التشغيل المتكرر
3. تأكد من أن كل شيء يبدو كما تريد

### الخطوة 5: إنشاء الفيديو النهائي
1. اضغط "🎬 إنشاء الفيديو النهائي"
2. اختر مكان حفظ الفيديو
3. انتظر حتى انتهاء المعالجة
4. ستحصل على فيديو جاهز للمشاركة!

## ⚙️ الإعدادات المتقدمة

### حفظ الإعدادات:
- اضغط "💾 حفظ الإعدادات" لحفظ تفضيلاتك
- سيتم تحميل الإعدادات تلقائياً في المرة القادمة

### ملف الإعدادات:
يتم حفظ الإعدادات في `video_editor_settings.json` ويشمل:
- إعدادات العلامة المائية
- تفضيلات التعليق
- مواقع الملفات المستخدمة

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### "مكتبة moviepy غير مثبتة":
```bash
pip install moviepy
```

#### "FFmpeg غير متاح":
1. نزل FFmpeg من الموقع الرسمي
2. أضفه إلى PATH في النظام
3. أو ضع ملفات FFmpeg في مجلد البرنامج

#### "فشل في تحميل الفيديو":
- تأكد من أن صيغة الفيديو مدعومة
- تأكد من أن الملف غير تالف
- جرب فيديو آخر للاختبار

#### "فشل في معالجة الفيديو":
- تأكد من وجود مساحة كافية على القرص
- تأكد من أن FFmpeg يعمل بشكل صحيح
- أغلق البرامج الأخرى التي قد تستخدم الذاكرة

## 📝 ملاحظات مهمة

### الأداء:
- معالجة الفيديوهات الطويلة قد تستغرق وقتاً أطول
- استخدم فيديوهات بدقة معقولة لتسريع المعالجة
- أغلق البرامج الأخرى أثناء المعالجة

### الجودة:
- يحافظ البرنامج على جودة الفيديو الأصلي
- العلامة المائية بدقة عالية وشفافية مناسبة
- الصوت يتم ضغطه بجودة AAC عالية

### التوافق:
- يعمل على Windows, macOS, Linux
- يدعم Python 3.7 وأحدث
- واجهة باللغة العربية والإنجليزية

## 🆘 الدعم والمساعدة

إذا واجهت أي مشاكل:
1. تأكد من تثبيت جميع المكتبات المطلوبة
2. تأكد من وجود FFmpeg
3. جرب إعادة تشغيل البرنامج
4. تحقق من رسائل الخطأ في النافذة

## 🧪 اختبار البرنامج

### اختبار سريع:
```bash
python test_video_editor.py
```

### إنشاء ملفات تجريبية:
```bash
python create_sample_video.py
```

## 📁 ملفات البرنامج

### الملفات الرئيسية:
- `video_editor_with_comments.py` - البرنامج الرئيسي
- `audio_recorder.py` - مسجل الصوت
- `install_and_run.py` - تثبيت المكتبات والتشغيل
- `run_video_editor.py` - تشغيل سريع
- `test_video_editor.py` - اختبار البرنامج
- `create_sample_video.py` - إنشاء ملفات تجريبية

### ملفات التشغيل:
- `تشغيل_محرر_الفيديو.bat` - تشغيل على Windows
- `requirements.txt` - قائمة المكتبات المطلوبة
- `دليل_محرر_الفيديو.md` - هذا الدليل

### ملفات الإعدادات:
- `video_editor_settings.json` - إعدادات محفوظة (ينشأ تلقائياً)

## 🎉 استمتع بالاستخدام!

البرنامج جاهز للاستخدام ويمكنك إنشاء فيديوهات احترافية مع تعليقاتك وعلامتك المائية الخاصة. شاركها على وسائل التواصل الاجتماعي واستمتع! 🚀

### طرق التشغيل المختلفة:

#### 1. التشغيل التلقائي (مع تثبيت المكتبات):
```bash
python install_and_run.py
```

#### 2. التشغيل السريع:
```bash
python run_video_editor.py
```

#### 3. التشغيل على Windows:
```
تشغيل_محرر_الفيديو.bat
```

#### 4. التشغيل المباشر:
```bash
python video_editor_with_comments.py
```
