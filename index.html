<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر الفيديو البسيط</title>
    <!-- استخدام Tailwind CSS للتصميم -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            transition: background-color 0.3s;
        }
        progress {
            accent-color: #4f46e5;
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen flex flex-col items-center justify-center p-4">

    <!-- === قسم التخصيص الشخصي (العلامة المائية) === -->
    <div id="user-profile" class="fixed top-4 left-4 z-10 flex items-center gap-3 bg-gray-800 bg-opacity-80 backdrop-blur-sm p-2 rounded-full shadow-lg">
        <label for="profile-pic-uploader" class="cursor-pointer">
            <img id="profile-pic-preview" src="https://placehold.co/48x48/374151/9ca3af?text=Avatar" alt="صورة الحساب" class="w-12 h-12 rounded-full object-cover border-2 border-gray-600 hover:border-indigo-500 transition">
        </label>
        <input type="file" id="profile-pic-uploader" class="hidden" accept="image/*">
        <input type="text" id="username-input" placeholder="اسم الحساب" class="bg-transparent text-white placeholder-gray-400 focus:outline-none w-32">
    </div>

    <div class="w-full max-w-4xl mx-auto bg-gray-800 rounded-2xl shadow-2xl p-6 md:p-8 space-y-6 mt-20 md:mt-0">
        
        <div class="text-center">
            <h1 class="text-3xl md:text-4xl font-bold text-indigo-400">محرر الفيديو السريع</h1>
            <p class="text-gray-400 mt-2">ارفع فيديو، أضف تعليقك وعلامتك المائية، ثم قم بتنزيله.</p>
        </div>

        <div id="upload-section" class="border-2 border-dashed border-gray-600 rounded-xl p-8 text-center cursor-pointer hover:border-indigo-500 hover:bg-gray-700 transition-all">
            <input type="file" id="video-uploader" class="hidden" accept="video/*">
            <label for="video-uploader" id="upload-label" class="cursor-pointer">
                <svg id="upload-icon" class="mx-auto h-12 w-12 text-gray-500" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
                <p id="upload-text" class="mt-4 text-lg">انقر هنا لاختيار فيديو</p>
                <p class="text-xs text-gray-500">MP4, WebM, OGG</p>
            </label>
        </div>

        <div id="editor-section" class="hidden space-y-6">
            <div>
                <h2 class="text-xl font-semibold mb-3">الفيديو الأصلي:</h2>
                <video id="video-player" class="w-full rounded-lg shadow-lg" loop playsinline></video>
            </div>
            <div>
                <label for="comment-input" class="block text-xl font-semibold mb-3">أضف تعليقك هنا:</label>
                <textarea id="comment-input" rows="3" class="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition" placeholder="مثال: لقطة رائعة!"></textarea>
            </div>
            <button id="generate-btn" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-3 px-4 rounded-lg transition-transform transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-indigo-500 text-lg">
                إنشاء وتنزيل الفيديو
            </button>
        </div>

        <div id="result-section" class="hidden text-center space-y-4">
            <h2 class="text-2xl font-bold text-green-400">اكتملت المعالجة بنجاح!</h2>
            <p class="text-gray-400">فيديوك الجديد جاهز (بدون صوت).</p>
            <video id="result-video" class="w-full rounded-lg" controls></video>
            <a id="download-link" class="inline-block w-full md:w-auto bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-8 rounded-lg transition-transform transform hover:scale-105 text-lg">
                تنزيل الفيديو الآن
            </a>
        </div>

        <div id="processing-section" class="hidden text-center space-y-4 p-6 bg-gray-700 rounded-lg">
             <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-400 mx-auto"></div>
            <p id="processing-status" class="text-xl font-semibold">جاري المعالجة...</p>
            <p id="processing-details" class="text-gray-400">جاري رسم التعليقات على الإطارات...</p>
            <progress id="progress-bar" value="0" max="100" class="w-full rounded-lg"></progress>
        </div>
    </div>
    
    <canvas id="video-canvas" class="hidden"></canvas>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- تعريف المتغيرات والعناصر ---
            const videoUploader = document.getElementById('video-uploader');
            const uploadSection = document.getElementById('upload-section');
            const editorSection = document.getElementById('editor-section');
            const videoPlayer = document.getElementById('video-player');
            const commentInput = document.getElementById('comment-input');
            const generateBtn = document.getElementById('generate-btn');
            const processingSection = document.getElementById('processing-section');
            const progressBar = document.getElementById('progress-bar');
            const resultSection = document.getElementById('result-section');
            const resultVideo = document.getElementById('result-video');
            const downloadLink = document.getElementById('download-link');
            const canvas = document.getElementById('video-canvas');
            const ctx = canvas.getContext('2d');
            const profilePicUploader = document.getElementById('profile-pic-uploader');
            const profilePicPreview = document.getElementById('profile-pic-preview');
            const usernameInput = document.getElementById('username-input');

            let sourceVideoFile;
            let profileImage = null;

            // --- معالجة رفع الصورة الشخصية ---
            profilePicUploader.addEventListener('change', (event) => {
                const file = event.target.files[0];
                if (file) {
                    const imageURL = URL.createObjectURL(file);
                    profilePicPreview.src = imageURL;
                    profileImage = new Image();
                    profileImage.src = imageURL;
                }
            });

            // --- معالجة رفع الفيديو ---
            videoUploader.addEventListener('change', (event) => {
                sourceVideoFile = event.target.files[0];
                if (sourceVideoFile) {
                    videoPlayer.src = URL.createObjectURL(sourceVideoFile);
                    videoPlayer.play();
                    uploadSection.classList.add('hidden');
                    editorSection.classList.remove('hidden');
                }
            });

            // --- عند النقر على زر إنشاء الفيديو ---
            generateBtn.addEventListener('click', () => {
                const commentText = commentInput.value.trim();
                const username = usernameInput.value.trim();

                editorSection.classList.add('hidden');
                resultSection.classList.add('hidden');
                processingSection.classList.remove('hidden');
                
                renderVideoWithComment(commentText, username, profileImage);
            });

            function renderVideoWithComment(text, username, pfpImage) {
                videoPlayer.pause();
                videoPlayer.currentTime = 0;

                const tempVideo = document.createElement('video');
                tempVideo.src = URL.createObjectURL(sourceVideoFile);
                
                tempVideo.onloadedmetadata = () => {
                    canvas.width = tempVideo.videoWidth;
                    canvas.height = tempVideo.videoHeight;
                    
                    const stream = canvas.captureStream(30);
                    const mediaRecorder = new MediaRecorder(stream, { mimeType: 'video/webm; codecs=vp9' });
                    let recordedChunks = [];

                    mediaRecorder.ondataavailable = (e) => {
                        if (e.data.size > 0) recordedChunks.push(e.data);
                    };
                    
                    mediaRecorder.onstop = () => {
                        const blob = new Blob(recordedChunks, { type: 'video/webm' });
                        const videoUrl = URL.createObjectURL(blob);
                        
                        resultVideo.src = videoUrl;
                        downloadLink.href = videoUrl;
                        downloadLink.download = `video_with_comment_${Date.now()}.webm`;

                        processingSection.classList.add('hidden');
                        resultSection.classList.remove('hidden');
                    };
                    
                    mediaRecorder.start();
                    tempVideo.play();
                    
                    function drawFrame() {
                        if (tempVideo.paused || tempVideo.ended) {
                            if (mediaRecorder.state === 'recording') mediaRecorder.stop();
                            return;
                        }
                        ctx.drawImage(tempVideo, 0, 0, canvas.width, canvas.height);
                        drawWatermark(username, pfpImage);
                        drawMainComment(text);
                        progressBar.value = (tempVideo.currentTime / tempVideo.duration) * 100;
                        requestAnimationFrame(drawFrame);
                    }
                    drawFrame();
                };
            }

            function drawWatermark(username, pfpImage) {
                ctx.save();
                ctx.globalAlpha = 0.7;
                const pfpSize = Math.max(30, Math.floor(canvas.height / 15));
                const margin = Math.floor(pfpSize / 3);
                const pfpX = margin;
                const pfpY = margin;
                if (pfpImage && pfpImage.complete) {
                    ctx.beginPath();
                    ctx.arc(pfpX + pfpSize / 2, pfpY + pfpSize / 2, pfpSize / 2, 0, Math.PI * 2, true);
                    ctx.closePath();
                    ctx.clip();
                    ctx.drawImage(pfpImage, pfpX, pfpY, pfpSize, pfpSize);
                    ctx.restore();
                    ctx.save();
                    ctx.globalAlpha = 0.7;
                }
                if (username) {
                    const fontSize = Math.floor(pfpSize / 2.5);
                    ctx.font = `bold ${fontSize}px Cairo`;
                    ctx.fillStyle = 'white';
                    ctx.textAlign = 'left'; // Changed to left for proper alignment
                    const textX = pfpX + pfpSize + margin;
                    const textY = pfpY + (pfpSize / 2) + (fontSize / 3);
                    ctx.fillText(username, textX, textY);
                }
                ctx.restore();
            }

            function drawMainComment(text) {
                const fontSize = Math.max(24, Math.floor(canvas.width / 25));
                ctx.font = `bold ${fontSize}px Cairo`;
                ctx.fillStyle = 'white';
                ctx.strokeStyle = 'black';
                ctx.lineWidth = Math.floor(fontSize / 10);
                ctx.textAlign = 'center';
                const x = canvas.width / 2;
                const y = canvas.height - (fontSize * 1.5);
                ctx.strokeText(text, x, y);
                ctx.fillText(text, x, y);
            }
        });
    </script>
</body>
</html>

