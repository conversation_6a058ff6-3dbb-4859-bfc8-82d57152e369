#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار محرر الفيديوهات
Test Video Editor
"""

import os
import sys

def test_imports():
    """اختبار استيراد المكتبات"""
    print("🔍 اختبار المكتبات...")
    
    libraries = [
        ("tkinter", "tkinter"),
        ("PIL", "PIL"),
        ("cv2", "opencv-python"),
        ("numpy", "numpy"),
        ("pygame", "pygame"),
    ]
    
    missing = []
    for lib, name in libraries:
        try:
            __import__(lib)
            print(f"✅ {name}")
        except ImportError:
            print(f"❌ {name}")
            missing.append(name)
    
    # اختبار moviepy
    try:
        import moviepy
        print("✅ moviepy")
    except ImportError:
        print("❌ moviepy")
        missing.append("moviepy")
    
    # اختبار pyaudio (اختياري)
    try:
        import pyaudio
        print("✅ pyaudio (للتسجيل الصوتي)")
    except ImportError:
        print("⚠️ pyaudio (اختياري - للتسجيل الصوتي)")
    
    return missing

def test_files():
    """اختبار وجود الملفات"""
    print("\n📁 اختبار الملفات...")
    
    required_files = [
        "video_editor_with_comments.py",
        "audio_recorder.py",
        "run_video_editor.py",
        "install_and_run.py"
    ]
    
    missing = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            missing.append(file)
    
    return missing

def test_video_editor():
    """اختبار تشغيل محرر الفيديوهات"""
    print("\n🎬 اختبار محرر الفيديوهات...")
    
    try:
        import video_editor_with_comments
        print("✅ تم استيراد محرر الفيديوهات بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في استيراد محرر الفيديوهات: {e}")
        return False

def test_audio_recorder():
    """اختبار مسجل الصوت"""
    print("\n🎤 اختبار مسجل الصوت...")
    
    try:
        import audio_recorder
        print("✅ تم استيراد مسجل الصوت بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في استيراد مسجل الصوت: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 50)
    print("🧪 اختبار محرر الفيديوهات مع التعليقات")
    print("=" * 50)
    
    # اختبار المكتبات
    missing_libs = test_imports()
    
    # اختبار الملفات
    missing_files = test_files()
    
    # اختبار البرامج
    video_editor_ok = test_video_editor()
    audio_recorder_ok = test_audio_recorder()
    
    # النتائج
    print("\n" + "=" * 50)
    print("📊 نتائج الاختبار")
    print("=" * 50)
    
    if missing_libs:
        print(f"❌ مكتبات مفقودة: {', '.join(missing_libs)}")
        print("💡 لتثبيت المكتبات المفقودة:")
        print("   pip install " + " ".join(missing_libs))
    else:
        print("✅ جميع المكتبات المطلوبة متوفرة")
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
    else:
        print("✅ جميع الملفات المطلوبة متوفرة")
    
    if video_editor_ok:
        print("✅ محرر الفيديوهات جاهز للاستخدام")
    else:
        print("❌ محرر الفيديوهات غير جاهز")
    
    if audio_recorder_ok:
        print("✅ مسجل الصوت جاهز للاستخدام")
    else:
        print("❌ مسجل الصوت غير جاهز")
    
    # التوصيات
    print("\n💡 التوصيات:")
    
    if not missing_libs and not missing_files and video_editor_ok:
        print("🎉 البرنامج جاهز للاستخدام!")
        print("🚀 يمكنك تشغيله بالأمر: python run_video_editor.py")
        print("📖 أو قراءة الدليل: دليل_محرر_الفيديو.md")
    else:
        print("🔧 يرجى حل المشاكل المذكورة أعلاه أولاً")
        if missing_libs:
            print("📦 ثبت المكتبات المفقودة")
        if not video_editor_ok:
            print("🎬 تأكد من ملف محرر الفيديوهات")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
