#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل فيديو الدائرة المضيئة والأعلام
Run glowing circle flags video
"""

import sys
import os

def main():
    """تشغيل إنشاء الفيديو"""
    print("=" * 60)
    print("🎬 إنشاء فيديو TikTok/YouTube Shorts - الدائرة المضيئة والأعلام")
    print("🎬 Creating TikTok/YouTube Shorts Video - Glowing Circle & Flags")
    print("=" * 60)
    
    try:
        # Import and run the video creator
        from glowing_circle_flags_video import GlowingCircleFlagsVideo
        
        print("\n📋 مواصفات الفيديو / Video Specifications:")
        print("   • الصيغة / Format: MP4 (H.264)")
        print("   • الدقة / Resolution: 1080x1920 (9:16 - TikTok/YouTube Shorts)")
        print("   • المدة / Duration: 25 seconds")
        print("   • معدل الإطارات / FPS: 30")
        print("   • المحتوى / Content: Glowing circle + animated flags")
        print("   • الأعلام / Flags: China 🇨🇳, Russia 🇷🇺, Turkey 🇹🇷 vs USA 🇺🇸")
        
        print("\n🚀 بدء إنشاء الفيديو...")
        print("🚀 Starting video creation...")
        
        # Create video
        creator = GlowingCircleFlagsVideo()
        output_file = creator.create_video()
        
        print("\n" + "=" * 60)
        print("✅ تم إنشاء الفيديو بنجاح!")
        print("✅ Video created successfully!")
        print(f"📁 الملف: {output_file}")
        print(f"📁 File: {output_file}")
        print("=" * 60)
        
        # Check file size
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
            print(f"📊 حجم الملف: {file_size:.2f} MB")
            print(f"📊 File size: {file_size:.2f} MB")
        
        print("\n🎯 الفيديو جاهز للنشر على:")
        print("🎯 Video ready for posting on:")
        print("   • TikTok")
        print("   • YouTube Shorts")
        print("   • Instagram Reels")
        print("   • Facebook Stories")
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("❌ Library import error")
        print("💡 تأكد من تثبيت المكتبات المطلوبة:")
        print("💡 Make sure to install required libraries:")
        print("   pip install -r requirements.txt")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الفيديو: {e}")
        print(f"❌ Error creating video: {e}")
        print("💡 تحقق من المكتبات والإعدادات")
        print("💡 Check libraries and settings")

if __name__ == "__main__":
    main()
