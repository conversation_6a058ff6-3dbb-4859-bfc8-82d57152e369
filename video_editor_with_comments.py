#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج تحرير الفيديوهات مع إضافة التعليقات والعلامة المائية
Video Editor with Comments and Watermark
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import pygame
import cv2
import numpy as np
from PIL import Image, ImageTk, ImageDraw, ImageFont
import threading
import os
import json
from datetime import datetime
import tempfile

try:
    from moviepy.editor import VideoFileClip, AudioFileClip, CompositeVideoClip, TextClip, ImageClip
    from moviepy.video.fx import resize, loop
    from moviepy.audio.fx import volumex
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False
    print("تحذير: مكتبة moviepy غير مثبتة. سيتم استخدام cv2 فقط.")

class VideoEditorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("محرر الفيديوهات مع التعليقات - Video Editor with Comments")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2c3e50')
        
        # متغيرات البرنامج
        self.current_video_path = None
        self.current_video = None
        self.is_playing = False
        self.is_looping = True
        self.video_thread = None
        self.audio_comment_path = None
        
        # إعدادات العلامة المائية
        self.watermark_settings = {
            'profile_image': None,
            'username': 'اسم المستخدم',
            'user_id': '@username',
            'position': 'bottom-right',
            'opacity': 0.8,
            'size': 100
        }
        
        # إعدادات التعليق
        self.comment_settings = {
            'text': '',
            'voice_file': None,
            'position': 'bottom',
            'font_size': 24,
            'color': 'white',
            'background': True
        }
        
        self.setup_ui()
        self.load_settings()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # إطار التحكم العلوي
        control_frame = ttk.LabelFrame(main_frame, text="التحكم في الفيديو", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # أزرار التحكم
        ttk.Button(control_frame, text="📁 رفع فيديو", command=self.load_video).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="▶️ تشغيل", command=self.play_video).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="⏸️ إيقاف", command=self.pause_video).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="🔄 تكرار", command=self.toggle_loop).pack(side=tk.LEFT, padx=5)
        
        # إطار المحتوى الرئيسي
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # إطار الفيديو (يسار)
        video_frame = ttk.LabelFrame(content_frame, text="معاينة الفيديو", padding=10)
        video_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # منطقة عرض الفيديو
        self.video_canvas = tk.Canvas(video_frame, bg='black', width=640, height=480)
        self.video_canvas.pack(fill=tk.BOTH, expand=True)
        
        # إطار الإعدادات (يمين)
        settings_frame = ttk.LabelFrame(content_frame, text="الإعدادات", padding=10)
        settings_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        
        # إعدادات التعليق
        comment_frame = ttk.LabelFrame(settings_frame, text="إعدادات التعليق", padding=10)
        comment_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(comment_frame, text="نص التعليق:").pack(anchor=tk.W)
        self.comment_text = tk.Text(comment_frame, height=3, width=30, font=('Arial', 10))
        self.comment_text.pack(fill=tk.X, pady=5)
        
        ttk.Button(comment_frame, text="🎤 تسجيل صوتي", command=self.record_audio).pack(fill=tk.X, pady=2)
        ttk.Button(comment_frame, text="📁 رفع ملف صوتي", command=self.load_audio).pack(fill=tk.X, pady=2)
        
        # إعدادات العلامة المائية
        watermark_frame = ttk.LabelFrame(settings_frame, text="العلامة المائية", padding=10)
        watermark_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(watermark_frame, text="اسم المستخدم:").pack(anchor=tk.W)
        self.username_entry = ttk.Entry(watermark_frame, width=25)
        self.username_entry.pack(fill=tk.X, pady=2)
        self.username_entry.insert(0, self.watermark_settings['username'])
        
        ttk.Label(watermark_frame, text="معرف الحساب:").pack(anchor=tk.W)
        self.userid_entry = ttk.Entry(watermark_frame, width=25)
        self.userid_entry.pack(fill=tk.X, pady=2)
        self.userid_entry.insert(0, self.watermark_settings['user_id'])
        
        ttk.Button(watermark_frame, text="📷 رفع صورة شخصية", command=self.load_profile_image).pack(fill=tk.X, pady=2)
        
        # موقع العلامة المائية
        ttk.Label(watermark_frame, text="موقع العلامة:").pack(anchor=tk.W)
        self.position_var = tk.StringVar(value=self.watermark_settings['position'])
        position_combo = ttk.Combobox(watermark_frame, textvariable=self.position_var, 
                                    values=['top-left', 'top-right', 'bottom-left', 'bottom-right'])
        position_combo.pack(fill=tk.X, pady=2)
        
        # إطار التصدير
        export_frame = ttk.LabelFrame(settings_frame, text="تصدير الفيديو", padding=10)
        export_frame.pack(fill=tk.X)
        
        ttk.Button(export_frame, text="🎬 إنشاء الفيديو النهائي", command=self.create_final_video).pack(fill=tk.X, pady=5)
        ttk.Button(export_frame, text="💾 حفظ الإعدادات", command=self.save_settings).pack(fill=tk.X, pady=2)
        
        # شريط التقدم
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=(10, 0))
        
        # شريط الحالة
        self.status_var = tk.StringVar(value="جاهز لرفع الفيديو...")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(5, 0))

    def load_video(self):
        """رفع ملف فيديو"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف فيديو",
            filetypes=[
                ("ملفات الفيديو", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv"),
                ("جميع الملفات", "*.*")
            ]
        )
        
        if file_path:
            self.current_video_path = file_path
            self.status_var.set(f"تم رفع الفيديو: {os.path.basename(file_path)}")
            self.load_video_preview()
    
    def load_video_preview(self):
        """تحميل معاينة الفيديو"""
        if not self.current_video_path:
            return
            
        try:
            # استخدام OpenCV لعرض الفيديو
            self.current_video = cv2.VideoCapture(self.current_video_path)
            
            # الحصول على معلومات الفيديو
            fps = self.current_video.get(cv2.CAP_PROP_FPS)
            frame_count = int(self.current_video.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps if fps > 0 else 0
            
            self.status_var.set(f"الفيديو محمل - المدة: {duration:.1f} ثانية")
            
            # عرض الإطار الأول
            self.show_frame()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الفيديو: {str(e)}")
    
    def show_frame(self):
        """عرض إطار من الفيديو"""
        if not self.current_video:
            return
            
        ret, frame = self.current_video.read()
        if ret:
            # تحويل الإطار لعرضه في tkinter
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # تغيير حجم الإطار ليناسب Canvas
            canvas_width = self.video_canvas.winfo_width()
            canvas_height = self.video_canvas.winfo_height()
            
            if canvas_width > 1 and canvas_height > 1:
                frame_resized = cv2.resize(frame_rgb, (canvas_width, canvas_height))
                
                # تحويل إلى PIL Image ثم PhotoImage
                pil_image = Image.fromarray(frame_resized)
                photo = ImageTk.PhotoImage(pil_image)
                
                # عرض الصورة في Canvas
                self.video_canvas.delete("all")
                self.video_canvas.create_image(canvas_width//2, canvas_height//2, image=photo)
                self.video_canvas.image = photo  # الاحتفاظ بمرجع
        else:
            # إعادة تشغيل الفيديو إذا كان التكرار مفعل
            if self.is_looping and self.is_playing:
                self.current_video.set(cv2.CAP_PROP_POS_FRAMES, 0)
    
    def play_video(self):
        """تشغيل الفيديو"""
        if not self.current_video_path:
            messagebox.showwarning("تحذير", "يرجى رفع فيديو أولاً")
            return
            
        self.is_playing = True
        self.status_var.set("جاري التشغيل...")
        
        if not self.video_thread or not self.video_thread.is_alive():
            self.video_thread = threading.Thread(target=self.video_playback_loop)
            self.video_thread.daemon = True
            self.video_thread.start()
    
    def video_playback_loop(self):
        """حلقة تشغيل الفيديو"""
        while self.is_playing and self.current_video:
            self.show_frame()
            # تأخير بناءً على معدل الإطارات
            fps = self.current_video.get(cv2.CAP_PROP_FPS)
            delay = 1.0 / fps if fps > 0 else 1.0 / 30
            threading.Event().wait(delay)
    
    def pause_video(self):
        """إيقاف تشغيل الفيديو"""
        self.is_playing = False
        self.status_var.set("متوقف")
    
    def toggle_loop(self):
        """تبديل وضع التكرار"""
        self.is_looping = not self.is_looping
        status = "مفعل" if self.is_looping else "معطل"
        self.status_var.set(f"التكرار {status}")
    
    def record_audio(self):
        """تسجيل تعليق صوتي"""
        try:
            from audio_recorder import AudioRecorder

            # فتح نافذة المسجل
            recorder = AudioRecorder(self.root)

            # انتظار إغلاق النافذة
            self.root.wait_window(recorder.recorder_window)

            # الحصول على الملف المسجل
            recorded_file = recorder.get_recorded_file()
            if recorded_file and os.path.exists(recorded_file):
                self.audio_comment_path = recorded_file
                self.status_var.set(f"تم تسجيل التعليق الصوتي: {os.path.basename(recorded_file)}")

        except ImportError:
            messagebox.showwarning("تحذير", "ملف مسجل الصوت غير موجود")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح مسجل الصوت: {str(e)}")
    
    def load_audio(self):
        """رفع ملف صوتي للتعليق"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف صوتي",
            filetypes=[
                ("ملفات صوتية", "*.mp3 *.wav *.aac *.ogg"),
                ("جميع الملفات", "*.*")
            ]
        )
        
        if file_path:
            self.audio_comment_path = file_path
            self.status_var.set(f"تم رفع التعليق الصوتي: {os.path.basename(file_path)}")
    
    def load_profile_image(self):
        """رفع صورة شخصية للعلامة المائية"""
        file_path = filedialog.askopenfilename(
            title="اختر صورة شخصية",
            filetypes=[
                ("ملفات الصور", "*.jpg *.jpeg *.png *.bmp *.gif"),
                ("جميع الملفات", "*.*")
            ]
        )
        
        if file_path:
            self.watermark_settings['profile_image'] = file_path
            self.status_var.set(f"تم رفع الصورة الشخصية: {os.path.basename(file_path)}")
    
    def create_final_video(self):
        """إنشاء الفيديو النهائي مع التعليقات والعلامة المائية"""
        if not self.current_video_path:
            messagebox.showwarning("تحذير", "يرجى رفع فيديو أولاً")
            return
        
        if not MOVIEPY_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة moviepy غير مثبتة. يرجى تثبيتها أولاً.")
            return
        
        # اختيار مكان حفظ الفيديو
        output_path = filedialog.asksaveasfilename(
            title="حفظ الفيديو النهائي",
            defaultextension=".mp4",
            filetypes=[("ملفات MP4", "*.mp4"), ("جميع الملفات", "*.*")]
        )
        
        if not output_path:
            return
        
        # تحديث الإعدادات من الواجهة
        self.update_settings_from_ui()
        
        # بدء معالجة الفيديو في thread منفصل
        processing_thread = threading.Thread(target=self.process_video, args=(output_path,))
        processing_thread.daemon = True
        processing_thread.start()
    
    def update_settings_from_ui(self):
        """تحديث الإعدادات من واجهة المستخدم"""
        self.watermark_settings['username'] = self.username_entry.get()
        self.watermark_settings['user_id'] = self.userid_entry.get()
        self.watermark_settings['position'] = self.position_var.get()
        self.comment_settings['text'] = self.comment_text.get("1.0", tk.END).strip()
    
    def process_video(self, output_path):
        """معالجة الفيديو وإضافة التعليقات والعلامة المائية"""
        try:
            self.status_var.set("جاري معالجة الفيديو...")
            self.progress_var.set(0)
            
            # تحميل الفيديو الأصلي
            video = VideoFileClip(self.current_video_path)
            self.progress_var.set(20)
            
            # إضافة التعليق الصوتي إذا كان متوفراً
            if self.audio_comment_path:
                audio_comment = AudioFileClip(self.audio_comment_path)
                # دمج الصوت الأصلي مع التعليق
                final_audio = video.audio.volumex(0.7)  # تقليل صوت الفيديو الأصلي
                comment_audio = audio_comment.volumex(1.0)  # صوت التعليق كامل
                
                # دمج الأصوات
                from moviepy.audio.AudioClip import CompositeAudioClip
                mixed_audio = CompositeAudioClip([final_audio, comment_audio])
                video = video.set_audio(mixed_audio)
            
            self.progress_var.set(40)
            
            # إضافة النص إذا كان متوفراً
            if self.comment_settings['text']:
                text_clip = TextClip(
                    self.comment_settings['text'],
                    fontsize=self.comment_settings['font_size'],
                    color=self.comment_settings['color'],
                    font='Arial-Bold'
                ).set_duration(video.duration).set_position('bottom')
                
                video = CompositeVideoClip([video, text_clip])
            
            self.progress_var.set(60)
            
            # إضافة العلامة المائية
            video = self.add_watermark(video)
            self.progress_var.set(80)
            
            # حفظ الفيديو النهائي
            video.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True
            )
            
            self.progress_var.set(100)
            self.status_var.set(f"تم حفظ الفيديو: {os.path.basename(output_path)}")
            
            messagebox.showinfo("نجح", f"تم إنشاء الفيديو بنجاح!\nالمسار: {output_path}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في معالجة الفيديو: {str(e)}")
            self.status_var.set("فشل في المعالجة")
        finally:
            self.progress_var.set(0)
    
    def add_watermark(self, video):
        """إضافة العلامة المائية للفيديو"""
        # إنشاء العلامة المائية كصورة
        watermark_img = self.create_watermark_image()
        
        if watermark_img:
            # تحويل الصورة إلى ImageClip
            watermark_clip = ImageClip(np.array(watermark_img), transparent=True)
            watermark_clip = watermark_clip.set_duration(video.duration)
            
            # تحديد موقع العلامة المائية
            position = self.get_watermark_position(video.size, watermark_img.size)
            watermark_clip = watermark_clip.set_position(position)
            
            # دمج العلامة المائية مع الفيديو
            video = CompositeVideoClip([video, watermark_clip])
        
        return video
    
    def create_watermark_image(self):
        """إنشاء صورة العلامة المائية"""
        try:
            # إنشاء صورة شفافة
            width, height = 300, 100
            watermark = Image.new('RGBA', (width, height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(watermark)
            
            # تحميل الخط
            try:
                font_large = ImageFont.truetype("arial.ttf", 20)
                font_small = ImageFont.truetype("arial.ttf", 16)
            except:
                font_large = ImageFont.load_default()
                font_small = ImageFont.load_default()
            
            # إضافة الصورة الشخصية إذا كانت متوفرة
            profile_x = 10
            if self.watermark_settings['profile_image']:
                try:
                    profile_img = Image.open(self.watermark_settings['profile_image'])
                    profile_img = profile_img.resize((60, 60))
                    # تحويل إلى دائرة
                    profile_img = self.make_circle_image(profile_img)
                    watermark.paste(profile_img, (10, 20), profile_img)
                    profile_x = 80
                except:
                    pass
            
            # إضافة النصوص
            username = self.watermark_settings['username']
            user_id = self.watermark_settings['user_id']
            
            # رسم النصوص مع خلفية شبه شفافة
            draw.rectangle([profile_x-5, 15, width-5, 85], fill=(0, 0, 0, 128))
            
            draw.text((profile_x, 20), username, fill=(255, 255, 255, 255), font=font_large)
            draw.text((profile_x, 50), user_id, fill=(200, 200, 200, 255), font=font_small)
            
            return watermark
            
        except Exception as e:
            print(f"خطأ في إنشاء العلامة المائية: {e}")
            return None
    
    def make_circle_image(self, img):
        """تحويل الصورة إلى شكل دائري"""
        size = min(img.size)
        img = img.resize((size, size))
        
        mask = Image.new('L', (size, size), 0)
        draw = ImageDraw.Draw(mask)
        draw.ellipse((0, 0, size, size), fill=255)
        
        result = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        result.paste(img, (0, 0))
        result.putalpha(mask)
        
        return result
    
    def get_watermark_position(self, video_size, watermark_size):
        """تحديد موقع العلامة المائية"""
        video_w, video_h = video_size
        watermark_w, watermark_h = watermark_size
        margin = 20
        
        position_map = {
            'top-left': (margin, margin),
            'top-right': (video_w - watermark_w - margin, margin),
            'bottom-left': (margin, video_h - watermark_h - margin),
            'bottom-right': (video_w - watermark_w - margin, video_h - watermark_h - margin)
        }
        
        return position_map.get(self.watermark_settings['position'], position_map['bottom-right'])
    
    def save_settings(self):
        """حفظ الإعدادات"""
        self.update_settings_from_ui()
        
        settings = {
            'watermark': self.watermark_settings,
            'comment': self.comment_settings
        }
        
        try:
            with open('video_editor_settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            
            self.status_var.set("تم حفظ الإعدادات")
            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح!")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الإعدادات: {str(e)}")
    
    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            if os.path.exists('video_editor_settings.json'):
                with open('video_editor_settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                if 'watermark' in settings:
                    self.watermark_settings.update(settings['watermark'])
                
                if 'comment' in settings:
                    self.comment_settings.update(settings['comment'])
                    
        except Exception as e:
            print(f"فشل في تحميل الإعدادات: {e}")

def main():
    """تشغيل البرنامج الرئيسي"""
    root = tk.Tk()
    app = VideoEditorApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
