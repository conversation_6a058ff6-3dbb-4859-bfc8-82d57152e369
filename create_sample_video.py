#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء فيديو تجريبي للاختبار
Create Sample Video for Testing
"""

import cv2
import numpy as np
import os
from datetime import datetime

def create_sample_video():
    """إنشاء فيديو تجريبي بسيط"""
    
    # إعدادات الفيديو
    width, height = 640, 480
    fps = 30
    duration = 5  # ثواني
    total_frames = fps * duration
    
    # إنشاء اسم الملف
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"sample_video_{timestamp}.mp4"
    
    # إعداد كاتب الفيديو
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(filename, fourcc, fps, (width, height))
    
    print(f"🎬 إنشاء فيديو تجريبي: {filename}")
    print(f"📐 الأبعاد: {width}x{height}")
    print(f"⏱️ المدة: {duration} ثواني")
    print(f"🎞️ معدل الإطارات: {fps} FPS")
    
    try:
        for frame_num in range(total_frames):
            # إنشاء إطار ملون متحرك
            frame = np.zeros((height, width, 3), dtype=np.uint8)
            
            # خلفية متدرجة
            for y in range(height):
                for x in range(width):
                    # ألوان متحركة بناءً على رقم الإطار والموقع
                    r = int(128 + 127 * np.sin(frame_num * 0.1 + x * 0.01))
                    g = int(128 + 127 * np.sin(frame_num * 0.1 + y * 0.01))
                    b = int(128 + 127 * np.sin(frame_num * 0.1 + (x + y) * 0.01))
                    
                    frame[y, x] = [b, g, r]  # BGR format
            
            # إضافة دائرة متحركة
            center_x = int(width // 2 + 100 * np.sin(frame_num * 0.2))
            center_y = int(height // 2 + 50 * np.cos(frame_num * 0.2))
            radius = int(30 + 20 * np.sin(frame_num * 0.3))
            
            cv2.circle(frame, (center_x, center_y), radius, (255, 255, 255), -1)
            cv2.circle(frame, (center_x, center_y), radius, (0, 0, 0), 2)
            
            # إضافة نص
            text = f"Frame {frame_num + 1}/{total_frames}"
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.7
            color = (255, 255, 255)
            thickness = 2
            
            # حساب موقع النص
            text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
            text_x = (width - text_size[0]) // 2
            text_y = height - 30
            
            # رسم خلفية للنص
            cv2.rectangle(frame, (text_x - 10, text_y - text_size[1] - 10), 
                         (text_x + text_size[0] + 10, text_y + 10), (0, 0, 0), -1)
            
            # رسم النص
            cv2.putText(frame, text, (text_x, text_y), font, font_scale, color, thickness)
            
            # إضافة عنوان
            title = "فيديو تجريبي - Sample Video"
            title_size = cv2.getTextSize(title, font, 1.0, 2)[0]
            title_x = (width - title_size[0]) // 2
            title_y = 40
            
            # خلفية العنوان
            cv2.rectangle(frame, (title_x - 10, title_y - title_size[1] - 10), 
                         (title_x + title_size[0] + 10, title_y + 10), (0, 0, 0), -1)
            
            # العنوان
            cv2.putText(frame, title, (title_x, title_y), font, 1.0, (0, 255, 255), 2)
            
            # كتابة الإطار
            out.write(frame)
            
            # عرض التقدم
            if (frame_num + 1) % 30 == 0:
                progress = (frame_num + 1) / total_frames * 100
                print(f"📊 التقدم: {progress:.1f}%")
        
        print("✅ تم إنشاء الفيديو بنجاح!")
        print(f"📁 المسار: {os.path.abspath(filename)}")
        
        return filename
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الفيديو: {e}")
        return None
        
    finally:
        out.release()

def create_sample_audio():
    """إنشاء ملف صوتي تجريبي"""
    try:
        import numpy as np
        import wave
        
        # إعدادات الصوت
        sample_rate = 44100
        duration = 3  # ثواني
        frequency = 440  # Hz (نوتة A)
        
        # إنشاء موجة صوتية
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # موجة جيبية بسيطة
        wave_data = np.sin(2 * np.pi * frequency * t)
        
        # إضافة تأثير fade in/out
        fade_samples = int(0.1 * sample_rate)  # 0.1 ثانية
        wave_data[:fade_samples] *= np.linspace(0, 1, fade_samples)
        wave_data[-fade_samples:] *= np.linspace(1, 0, fade_samples)
        
        # تحويل إلى 16-bit
        wave_data = (wave_data * 32767).astype(np.int16)
        
        # حفظ الملف
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"sample_audio_{timestamp}.wav"
        
        with wave.open(filename, 'w') as wav_file:
            wav_file.setnchannels(1)  # mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(wave_data.tobytes())
        
        print(f"🎵 تم إنشاء ملف صوتي تجريبي: {filename}")
        return filename
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف الصوتي: {e}")
        return None

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🎬 إنشاء ملفات تجريبية لمحرر الفيديوهات")
    print("=" * 50)
    
    # إنشاء فيديو تجريبي
    video_file = create_sample_video()
    
    print("\n" + "-" * 30)
    
    # إنشاء ملف صوتي تجريبي
    audio_file = create_sample_audio()
    
    print("\n" + "=" * 50)
    print("📋 الملفات المنشأة:")
    
    if video_file:
        print(f"🎬 فيديو: {video_file}")
    
    if audio_file:
        print(f"🎵 صوت: {audio_file}")
    
    print("\n💡 يمكنك الآن استخدام هذه الملفات لاختبار محرر الفيديوهات!")
    print("🚀 تشغيل المحرر: python run_video_editor.py")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
