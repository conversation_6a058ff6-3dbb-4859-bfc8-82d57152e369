#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فيديو قصير محسن بصيغة TikTok/YouTube Shorts مع دائرة مضيئة وأعلام متحركة
Enhanced TikTok/YouTube Shorts video with glowing circle and animated flags
"""

import numpy as np
from PIL import Image, ImageDraw, ImageFont, ImageFilter
from moviepy.editor import VideoClip
import math
import os

class EnhancedGlowingFlagsVideo:
    def __init__(self):
        self.width = 1080
        self.height = 1920  # 9:16 aspect ratio
        self.duration = 25  # 25 seconds
        self.fps = 30
        
        # Colors
        self.bg_color = (5, 5, 15)  # Very dark background
        self.glow_color = (100, 200, 255)  # Blue glow
        self.text_color = (255, 255, 255)  # White text
        self.accent_color = (255, 215, 0)  # Gold accent
        
        # Circle properties
        self.circle_radius = 280
        self.circle_center = (self.width // 2, self.height // 2)
        
        # Flag emojis
        self.flags = {
            'china': '🇨🇳',
            'russia': '🇷🇺', 
            'turkey': '🇹🇷',
            'usa': '🇺🇸'
        }
        
    def get_font(self, size):
        """Get the best available font"""
        font_paths = [
            "C:/Windows/Fonts/arial.ttf",
            "C:/Windows/Fonts/calibri.ttf",
            "arial.ttf",
            "calibri.ttf"
        ]
        
        for font_path in font_paths:
            try:
                return ImageFont.truetype(font_path, size)
            except:
                continue
        
        return ImageFont.load_default()
    
    def create_glowing_circle(self, t):
        """Create animated glowing circle"""
        img = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Rotation and pulsing
        rotation = (t * 20) % 360
        pulse = 1 + 0.1 * math.sin(t * 4)
        
        # Multiple glow layers
        for i in range(15):
            alpha = int(255 * (1 - i/15) * 0.4 * pulse)
            radius = (self.circle_radius + i * 8) * pulse
            
            # Slight rotation offset
            offset_x = math.cos(math.radians(rotation + i * 5)) * 3
            offset_y = math.sin(math.radians(rotation + i * 5)) * 3
            
            center_x = self.circle_center[0] + offset_x
            center_y = self.circle_center[1] + offset_y
            
            bbox = [
                center_x - radius,
                center_y - radius,
                center_x + radius,
                center_y + radius
            ]
            
            # Gradient colors
            if i < 5:
                color = (*self.accent_color, alpha)
            else:
                color = (*self.glow_color, alpha)
            
            draw.ellipse(bbox, outline=color, width=4)
        
        return img
    
    def create_flag_with_glow(self, flag_emoji, size=90):
        """Create flag with enhanced glow effect"""
        img = Image.new('RGBA', (size + 40, size + 40), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Try to get emoji font
        try:
            font = ImageFont.truetype("seguiemj.ttf", size-5)
        except:
            try:
                font = ImageFont.truetype("C:/Windows/Fonts/seguiemj.ttf", size-5)
            except:
                font = self.get_font(size-10)
        
        # Calculate text position
        bbox = draw.textbbox((0, 0), flag_emoji, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (img.width - text_width) // 2
        y = (img.height - text_height) // 2
        
        # Create glow layers
        for glow_size in [8, 5, 3]:
            glow_img = Image.new('RGBA', img.size, (0, 0, 0, 0))
            glow_draw = ImageDraw.Draw(glow_img)
            glow_draw.text((x, y), flag_emoji, font=font, fill=(255, 255, 255, 100))
            glow_img = glow_img.filter(ImageFilter.GaussianBlur(radius=glow_size))
            img = Image.alpha_composite(img, glow_img)
        
        # Main flag
        draw.text((x, y), flag_emoji, font=font, fill=(255, 255, 255, 255))
        
        return img
    
    def calculate_flag_positions(self, t):
        """Calculate animated flag positions"""
        # Animation phases
        phase1_duration = 8  # Initial positioning
        phase2_duration = 12  # Movement towards center
        phase3_duration = 5   # Final confrontation
        
        if t <= phase1_duration:
            # Phase 1: Flags appear and position
            progress = t / phase1_duration
            alpha = min(progress * 2, 1.0)
            
            positions = {
                'china': (self.circle_center[0] - 180, self.circle_center[1] - 80),
                'russia': (self.circle_center[0] - 220, self.circle_center[1]),
                'turkey': (self.circle_center[0] - 180, self.circle_center[1] + 80),
                'usa': (self.circle_center[0] + 200, self.circle_center[1])
            }
            
        elif t <= phase1_duration + phase2_duration:
            # Phase 2: Movement towards center
            progress = (t - phase1_duration) / phase2_duration
            eased_progress = 1 - (1 - progress) ** 2  # Ease out
            
            # Start positions
            start_positions = {
                'china': (self.circle_center[0] - 180, self.circle_center[1] - 80),
                'russia': (self.circle_center[0] - 220, self.circle_center[1]),
                'turkey': (self.circle_center[0] - 180, self.circle_center[1] + 80),
                'usa': (self.circle_center[0] + 200, self.circle_center[1])
            }
            
            # End positions
            end_positions = {
                'china': (self.circle_center[0] - 120, self.circle_center[1] - 60),
                'russia': (self.circle_center[0] - 140, self.circle_center[1]),
                'turkey': (self.circle_center[0] - 120, self.circle_center[1] + 60),
                'usa': (self.circle_center[0] + 130, self.circle_center[1])
            }
            
            positions = {}
            for flag in start_positions:
                start_x, start_y = start_positions[flag]
                end_x, end_y = end_positions[flag]
                
                x = start_x + (end_x - start_x) * eased_progress
                y = start_y + (end_y - start_y) * eased_progress
                positions[flag] = (x, y)
            
            alpha = 1.0
            
        else:
            # Phase 3: Final confrontation with slight oscillation
            oscillation = math.sin((t - phase1_duration - phase2_duration) * 8) * 5
            
            positions = {
                'china': (self.circle_center[0] - 120 + oscillation, self.circle_center[1] - 60),
                'russia': (self.circle_center[0] - 140 + oscillation, self.circle_center[1]),
                'turkey': (self.circle_center[0] - 120 + oscillation, self.circle_center[1] + 60),
                'usa': (self.circle_center[0] + 130 - oscillation, self.circle_center[1])
            }
            alpha = 1.0
        
        return positions, alpha
    
    def add_title_text(self, img, t):
        """Add animated title text"""
        draw = ImageDraw.Draw(img)
        
        # Text properties
        title = "Which country will win?"
        font_size = 65
        font = self.get_font(font_size)
        
        # Text position
        bbox = draw.textbbox((0, 0), title, font=font)
        text_width = bbox[2] - bbox[0]
        x = (self.width - text_width) // 2
        y = 120
        
        # Pulsing glow effect
        glow_intensity = 0.7 + 0.3 * math.sin(t * 3)
        
        # Multiple glow layers
        for glow_radius in [8, 5, 3]:
            glow_img = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
            glow_draw = ImageDraw.Draw(glow_img)
            
            alpha = int(255 * glow_intensity * (1 - glow_radius/10))
            glow_color = (*self.glow_color, alpha)
            
            glow_draw.text((x, y), title, font=font, fill=glow_color)
            glow_img = glow_img.filter(ImageFilter.GaussianBlur(radius=glow_radius))
            img.paste(glow_img, (0, 0), glow_img)
        
        # Main text with stroke
        for offset in [(-2, -2), (-2, 2), (2, -2), (2, 2)]:
            draw.text((x + offset[0], y + offset[1]), title, font=font, fill=(0, 0, 0, 255))
        
        draw.text((x, y), title, font=font, fill=self.text_color)
        
        return img
    
    def create_background_particles(self, t):
        """Create animated background particles"""
        img = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Floating particles
        for i in range(30):
            # Particle movement
            angle = (t * 30 + i * 12) % 360
            radius = 300 + math.sin(t * 1.5 + i) * 100
            
            x = self.circle_center[0] + math.cos(math.radians(angle)) * radius
            y = self.circle_center[1] + math.sin(math.radians(angle)) * radius
            
            # Particle properties
            size = 2 + math.sin(t * 4 + i) * 1.5
            alpha = int(80 + math.sin(t * 3 + i) * 40)
            
            if 0 <= x < self.width and 0 <= y < self.height:
                color = (*self.glow_color, alpha)
                draw.ellipse([x-size, y-size, x+size, y+size], fill=color)
        
        return img
    
    def create_frame(self, t):
        """Create a single frame"""
        # Background
        img = Image.new('RGB', (self.width, self.height), self.bg_color)
        
        # Background particles
        particles = self.create_background_particles(t)
        img.paste(particles, (0, 0), particles)
        
        # Glowing circle
        circle = self.create_glowing_circle(t)
        img.paste(circle, (0, 0), circle)
        
        # Flags
        positions, alpha = self.calculate_flag_positions(t)
        
        for flag_name, pos in positions.items():
            flag_img = self.create_flag_with_glow(self.flags[flag_name])
            
            # Apply alpha
            if alpha < 1.0:
                flag_img = flag_img.copy()
                flag_img.putalpha(int(255 * alpha))
            
            paste_x = int(pos[0] - flag_img.width // 2)
            paste_y = int(pos[1] - flag_img.height // 2)
            
            if 0 <= paste_x < self.width - flag_img.width and 0 <= paste_y < self.height - flag_img.height:
                img.paste(flag_img, (paste_x, paste_y), flag_img)
        
        # Title text
        img = self.add_title_text(img, t)
        
        return np.array(img)
    
    def create_video(self):
        """Create the complete video"""
        print("إنشاء فيديو الدائرة المضيئة والأعلام المحسن...")
        print("Creating enhanced glowing circle flags video...")
        
        # Create video clip
        video_clip = VideoClip(self.create_frame, duration=self.duration)
        video_clip = video_clip.set_fps(self.fps)
        
        # Export video
        output_path = "enhanced_glowing_flags_tiktok.mp4"
        print(f"حفظ الفيديو في: {output_path}")
        print(f"Saving video to: {output_path}")
        
        video_clip.write_videofile(
            output_path,
            fps=self.fps,
            codec='libx264',
            preset='medium',
            ffmpeg_params=['-crf', '18']  # High quality
        )
        
        print("تم إنشاء الفيديو بنجاح!")
        print("Video created successfully!")
        
        return output_path

def main():
    """Main function"""
    try:
        creator = EnhancedGlowingFlagsVideo()
        output_file = creator.create_video()
        
        print(f"\n✅ الفيديو جاهز: {output_file}")
        print(f"✅ Video ready: {output_file}")
        
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file) / (1024 * 1024)
            print(f"📊 حجم الملف: {file_size:.2f} MB")
            print(f"📊 File size: {file_size:.2f} MB")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
