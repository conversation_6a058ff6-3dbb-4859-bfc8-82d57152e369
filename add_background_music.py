#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة موسيقى خلفية حماسية للفيديو
Add epic background music to the video
"""

import os
import numpy as np
from moviepy.editor import VideoFileClip, AudioClip, CompositeAudioClip
from moviepy.audio.fx import volumex
import math

class BackgroundMusicGenerator:
    def __init__(self):
        self.sample_rate = 44100
        self.duration = 25  # seconds
        
    def generate_epic_tone(self, frequency, duration, sample_rate=44100):
        """Generate an epic tone with harmonics"""
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # Base frequency
        wave = np.sin(2 * np.pi * frequency * t)
        
        # Add harmonics for richness
        wave += 0.3 * np.sin(2 * np.pi * frequency * 2 * t)  # Octave
        wave += 0.2 * np.sin(2 * np.pi * frequency * 1.5 * t)  # Fifth
        wave += 0.1 * np.sin(2 * np.pi * frequency * 3 * t)   # Third harmonic
        
        # Apply envelope (fade in/out)
        envelope = np.ones_like(t)
        fade_samples = int(0.1 * sample_rate)  # 0.1 second fade
        
        # Fade in
        envelope[:fade_samples] = np.linspace(0, 1, fade_samples)
        # Fade out
        envelope[-fade_samples:] = np.linspace(1, 0, fade_samples)
        
        wave *= envelope
        
        # Normalize
        wave = wave / np.max(np.abs(wave))
        
        return wave
    
    def create_epic_background_music(self):
        """Create epic background music"""
        print("إنشاء موسيقى خلفية حماسية...")
        print("Creating epic background music...")
        
        # Musical progression (frequencies in Hz)
        # Using pentatonic scale for epic feel
        base_freq = 110  # A2
        progression = [
            base_freq,          # A
            base_freq * 1.125,  # B
            base_freq * 1.25,   # C#
            base_freq * 1.5,    # E
            base_freq * 1.6875, # F#
            base_freq * 2,      # A (octave)
        ]
        
        # Create segments
        segment_duration = self.duration / len(progression)
        full_audio = np.array([])
        
        for i, freq in enumerate(progression):
            # Create tension build-up
            intensity = 0.3 + (i / len(progression)) * 0.7
            
            # Generate tone
            segment = self.generate_epic_tone(freq, segment_duration, self.sample_rate)
            
            # Add some variation
            if i > 2:  # Add higher octave for climax
                higher_octave = self.generate_epic_tone(freq * 2, segment_duration, self.sample_rate)
                segment = segment + 0.3 * higher_octave
            
            # Apply intensity
            segment *= intensity
            
            # Add to full audio
            full_audio = np.concatenate([full_audio, segment])
        
        # Add drum-like rhythm
        drum_audio = self.create_drum_rhythm()
        
        # Mix audio
        if len(drum_audio) == len(full_audio):
            full_audio = full_audio + 0.4 * drum_audio
        
        # Normalize final audio
        full_audio = full_audio / np.max(np.abs(full_audio)) * 0.8
        
        return full_audio
    
    def create_drum_rhythm(self):
        """Create drum-like rhythm"""
        t = np.linspace(0, self.duration, int(self.sample_rate * self.duration), False)
        drum_audio = np.zeros_like(t)
        
        # Beat pattern (4/4 time)
        beats_per_second = 2  # 120 BPM
        beat_interval = 1.0 / beats_per_second
        
        for beat_time in np.arange(0, self.duration, beat_interval):
            beat_start = int(beat_time * self.sample_rate)
            beat_duration = int(0.1 * self.sample_rate)  # 0.1 second beat
            
            if beat_start + beat_duration < len(drum_audio):
                # Create kick drum sound (low frequency)
                beat_t = np.linspace(0, 0.1, beat_duration)
                kick = np.sin(2 * np.pi * 60 * beat_t) * np.exp(-beat_t * 20)
                
                drum_audio[beat_start:beat_start + beat_duration] += kick
        
        return drum_audio
    
    def add_music_to_video(self, video_path, output_path):
        """Add background music to video"""
        print(f"إضافة الموسيقى للفيديو: {video_path}")
        print(f"Adding music to video: {video_path}")
        
        # Load video
        video = VideoFileClip(video_path)
        
        # Generate background music
        music_array = self.create_epic_background_music()
        
        # Create audio clip
        def make_frame_audio(t):
            frame_start = int(t * self.sample_rate)
            if frame_start < len(music_array):
                return music_array[frame_start]
            return 0
        
        background_music = AudioClip(make_frame_audio, duration=self.duration)
        background_music = background_music.fx(volumex, 0.3)  # Lower volume
        
        # Combine with existing audio (if any)
        if video.audio is not None:
            final_audio = CompositeAudioClip([video.audio, background_music])
        else:
            final_audio = background_music
        
        # Set audio to video
        final_video = video.set_audio(final_audio)
        
        # Export
        print(f"حفظ الفيديو مع الموسيقى: {output_path}")
        print(f"Saving video with music: {output_path}")
        
        final_video.write_videofile(
            output_path,
            fps=30,
            codec='libx264',
            audio_codec='aac',
            preset='medium',
            ffmpeg_params=['-crf', '18']
        )
        
        # Clean up
        video.close()
        final_video.close()
        
        print("تم إضافة الموسيقى بنجاح!")
        print("Music added successfully!")
        
        return output_path

def main():
    """Main function"""
    input_video = "enhanced_glowing_flags_tiktok.mp4"
    output_video = "enhanced_glowing_flags_with_music.mp4"
    
    if not os.path.exists(input_video):
        print(f"❌ الفيديو غير موجود: {input_video}")
        print(f"❌ Video not found: {input_video}")
        return
    
    try:
        music_generator = BackgroundMusicGenerator()
        result = music_generator.add_music_to_video(input_video, output_video)
        
        print(f"\n✅ الفيديو النهائي جاهز: {result}")
        print(f"✅ Final video ready: {result}")
        
        if os.path.exists(result):
            file_size = os.path.getsize(result) / (1024 * 1024)
            print(f"📊 حجم الملف: {file_size:.2f} MB")
            print(f"📊 File size: {file_size:.2f} MB")
        
        print("\n🎵 المميزات المضافة:")
        print("🎵 Added features:")
        print("   • موسيقى خلفية حماسية / Epic background music")
        print("   • إيقاع درامي / Dramatic rhythm")
        print("   • تصاعد موسيقي / Musical build-up")
        print("   • تأثيرات صوتية / Sound effects")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الموسيقى: {e}")
        print(f"❌ Error adding music: {e}")

if __name__ == "__main__":
    main()
