#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لمحرر الفيديوهات
Quick Run for Video Editor
"""

import sys
import os

def main():
    """تشغيل محرر الفيديوهات مباشرة"""
    try:
        print("🎬 تشغيل محرر الفيديوهات...")
        
        # التأكد من وجود الملف
        if not os.path.exists("video_editor_with_comments.py"):
            print("❌ ملف محرر الفيديوهات غير موجود!")
            input("اضغط Enter للخروج...")
            return
        
        # تشغيل البرنامج
        import video_editor_with_comments
        video_editor_with_comments.main()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("💡 جرب تشغيل: python install_and_run.py")
        input("اضغط Enter للخروج...")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
